#!/usr/bin/env python3
"""
MySQL连接测试脚本
测试项目中MySQL相关的功能
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent
llm_backend_path = project_root / "llm_backend"
sys.path.insert(0, str(llm_backend_path))

# 设置环境变量文件路径
env_file = llm_backend_path / ".env"
os.environ.setdefault("ENV_FILE", str(env_file))

import pymysql
import aiomysql
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(env_file)

def test_basic_mysql_connection():
    """测试基本的MySQL连接"""
    print("=" * 60)
    print("🔍 测试基本MySQL连接 (pymysql)")
    print("=" * 60)
    
    try:
        # 从环境变量获取配置
        config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'user': os.getenv('DB_USER', 'root'),
            'password': os.getenv('DB_PASSWORD', '123456'),
            'charset': 'utf8mb4'
        }
        
        print(f"连接配置: {config['host']}:{config['port']}")
        print(f"用户: {config['user']}")
        print(f"密码: {'*' * len(config['password'])}")
        
        # 测试连接
        connection = pymysql.connect(**config)
        print("✅ MySQL连接成功!")
        
        # 测试基本查询
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"📊 MySQL版本: {version[0]}")
            
            # 显示数据库列表
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            print(f"📁 可用数据库: {[db[0] for db in databases]}")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return False

def test_database_creation():
    """测试数据库创建"""
    print("\n" + "=" * 60)
    print("🏗️  测试数据库创建")
    print("=" * 60)
    
    try:
        config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'user': os.getenv('DB_USER', 'root'),
            'password': os.getenv('DB_PASSWORD', '123456'),
            'charset': 'utf8mb4'
        }
        
        db_name = os.getenv('DB_NAME', 'assist_gen')
        
        connection = pymysql.connect(**config)
        
        with connection.cursor() as cursor:
            # 检查数据库是否存在
            cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
            exists = cursor.fetchone()
            
            if exists:
                print(f"✅ 数据库 '{db_name}' 已存在")
            else:
                # 创建数据库
                cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                print(f"✅ 数据库 '{db_name}' 创建成功")
            
            connection.commit()
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库创建失败: {e}")
        return False

async def test_async_mysql_connection():
    """测试异步MySQL连接"""
    print("\n" + "=" * 60)
    print("🔄 测试异步MySQL连接 (aiomysql)")
    print("=" * 60)
    
    try:
        config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'user': os.getenv('DB_USER', 'root'),
            'password': os.getenv('DB_PASSWORD', '123456'),
            'db': os.getenv('DB_NAME', 'assist_gen'),
            'charset': 'utf8mb4'
        }
        
        # 测试异步连接
        connection = await aiomysql.connect(**config)
        print("✅ 异步MySQL连接成功!")
        
        async with connection.cursor() as cursor:
            await cursor.execute("SELECT VERSION()")
            version = await cursor.fetchone()
            print(f"📊 MySQL版本: {version[0]}")
            
            await cursor.execute("SELECT DATABASE()")
            current_db = await cursor.fetchone()
            print(f"📁 当前数据库: {current_db[0]}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 异步MySQL连接失败: {e}")
        return False

def test_sqlalchemy_connection():
    """测试SQLAlchemy连接"""
    print("\n" + "=" * 60)
    print("🔧 测试SQLAlchemy连接")
    print("=" * 60)
    
    try:
        # 构建连接URL
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '3306')
        db_user = os.getenv('DB_USER', 'root')
        db_password = os.getenv('DB_PASSWORD', '123456')
        db_name = os.getenv('DB_NAME', 'assist_gen')
        
        database_url = f"mysql+pymysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        print(f"连接URL: mysql+pymysql://{db_user}:***@{db_host}:{db_port}/{db_name}")
        
        # 创建引擎
        engine = create_engine(database_url)
        
        # 测试连接
        with engine.connect() as connection:
            result = connection.execute(text("SELECT VERSION()"))
            version = result.fetchone()
            print(f"✅ SQLAlchemy连接成功!")
            print(f"📊 MySQL版本: {version[0]}")
            
            # 测试表查询
            result = connection.execute(text("SHOW TABLES"))
            tables = result.fetchall()
            print(f"📋 数据库中的表: {[table[0] for table in tables] if tables else '无表'}")
        
        return True
        
    except Exception as e:
        print(f"❌ SQLAlchemy连接失败: {e}")
        return False

async def test_async_sqlalchemy_connection():
    """测试异步SQLAlchemy连接"""
    print("\n" + "=" * 60)
    print("⚡ 测试异步SQLAlchemy连接")
    print("=" * 60)
    
    try:
        # 构建异步连接URL
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '3306')
        db_user = os.getenv('DB_USER', 'root')
        db_password = os.getenv('DB_PASSWORD', '123456')
        db_name = os.getenv('DB_NAME', 'assist_gen')
        
        database_url = f"mysql+aiomysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        print(f"异步连接URL: mysql+aiomysql://{db_user}:***@{db_host}:{db_port}/{db_name}")
        
        # 创建异步引擎
        engine = create_async_engine(database_url)
        
        # 测试连接
        async with engine.begin() as connection:
            result = await connection.execute(text("SELECT VERSION()"))
            version = result.fetchone()
            print(f"✅ 异步SQLAlchemy连接成功!")
            print(f"📊 MySQL版本: {version[0]}")
            
            # 测试表查询
            result = await connection.execute(text("SHOW TABLES"))
            tables = result.fetchall()
            print(f"📋 数据库中的表: {[table[0] for table in tables] if tables else '无表'}")
        
        await engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ 异步SQLAlchemy连接失败: {e}")
        return False

def test_project_config():
    """测试项目配置"""
    print("\n" + "=" * 60)
    print("⚙️  测试项目配置")
    print("=" * 60)
    
    try:
        from app.core.config import settings
        
        print(f"✅ 配置加载成功!")
        print(f"📊 数据库主机: {settings.DB_HOST}")
        print(f"📊 数据库端口: {settings.DB_PORT}")
        print(f"📊 数据库用户: {settings.DB_USER}")
        print(f"📊 数据库名称: {settings.DB_NAME}")
        print(f"📊 数据库URL: {settings.DATABASE_URL.replace(settings.DB_PASSWORD, '***')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 项目配置加载失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始MySQL连接测试")
    print("=" * 60)
    
    results = []
    
    # 基本连接测试
    results.append(test_basic_mysql_connection())
    
    # 数据库创建测试
    results.append(test_database_creation())
    
    # 异步连接测试
    results.append(await test_async_mysql_connection())
    
    # SQLAlchemy测试
    results.append(test_sqlalchemy_connection())
    
    # 异步SQLAlchemy测试
    results.append(await test_async_sqlalchemy_connection())
    
    # 项目配置测试
    results.append(test_project_config())
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    test_names = [
        "基本MySQL连接",
        "数据库创建",
        "异步MySQL连接",
        "SQLAlchemy连接",
        "异步SQLAlchemy连接",
        "项目配置"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    print(f"\n🎯 总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试都通过了！MySQL配置正确。")
    else:
        print("⚠️  部分测试失败，请检查MySQL配置和连接。")

if __name__ == "__main__":
    asyncio.run(main())
