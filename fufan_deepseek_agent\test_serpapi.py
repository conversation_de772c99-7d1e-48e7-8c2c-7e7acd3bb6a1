#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SerpAPI测试脚本
测试Google搜索功能
"""

import sys
import os
import traceback
import asyncio
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "llm_backend"))

def test_serpapi_direct():
    """直接测试SerpAPI"""
    print("\n" + "=" * 60)
    print("🔍 直接测试SerpAPI")
    print("=" * 60)
    
    try:
        import requests
        
        # 使用您提供的API密钥
        api_key = "b7ff3b529bbd14cea92bd8a5e06094773567c335c5079322410b3be1793da560"
        
        # 测试查询
        query = "Python编程语言最新版本"
        
        params = {
            "engine": "google",
            "q": query,
            "api_key": api_key,
            "num": 3,
            "hl": "zh-CN",
            "gl": "cn"
        }
        
        print(f"🔍 搜索查询: {query}")
        print(f"🔑 API密钥: {api_key[:20]}...")
        
        response = requests.get(
            "https://serpapi.com/search",
            params=params,
            timeout=15
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # 检查搜索结果
            if "organic_results" in data:
                results = data["organic_results"]
                print(f"✅ 找到 {len(results)} 个搜索结果")
                
                for i, result in enumerate(results[:3], 1):
                    print(f"\n📄 结果 {i}:")
                    print(f"  标题: {result.get('title', 'N/A')}")
                    print(f"  链接: {result.get('link', 'N/A')}")
                    print(f"  摘要: {result.get('snippet', 'N/A')[:100]}...")
                
                return True
            else:
                print("❌ 响应中没有organic_results")
                print(f"响应内容: {data}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 直接测试SerpAPI失败: {e}")
        traceback.print_exc()
        return False

def test_search_tool():
    """测试项目中的SearchTool类"""
    print("\n" + "=" * 60)
    print("🛠️ 测试项目SearchTool类")
    print("=" * 60)
    
    try:
        from app.tools.search import SearchTool
        
        # 创建搜索工具实例
        search_tool = SearchTool()
        print("✅ SearchTool实例创建成功")
        
        # 测试搜索
        query = "人工智能最新发展"
        print(f"🔍 搜索查询: {query}")
        
        results = search_tool.search(query, num_results=3)
        
        if results:
            print(f"✅ 搜索成功，找到 {len(results)} 个结果")
            
            for i, result in enumerate(results, 1):
                print(f"\n📄 结果 {i}:")
                print(f"  标题: {result.get('title', 'N/A')}")
                print(f"  链接: {result.get('url', 'N/A')}")
                print(f"  摘要: {result.get('snippet', 'N/A')[:100]}...")
            
            return True
        else:
            print("❌ 搜索结果为空")
            return False
            
    except Exception as e:
        print(f"❌ SearchTool测试失败: {e}")
        traceback.print_exc()
        return False

async def test_search_service():
    """测试SearchService"""
    print("\n" + "=" * 60)
    print("🚀 测试SearchService")
    print("=" * 60)
    
    try:
        from app.services.search_service import SearchService
        
        # 创建搜索服务实例
        search_service = SearchService()
        print("✅ SearchService实例创建成功")
        
        # 测试查询
        query = "今天的天气怎么样？"
        print(f"💬 用户查询: {query}")
        
        print("🔄 开始流式生成...")
        response_parts = []
        
        async for chunk in search_service.generate_stream(query):
            response_parts.append(chunk)
            print(chunk, end="", flush=True)
        
        full_response = "".join(response_parts)
        print(f"\n\n✅ 完整响应长度: {len(full_response)} 字符")
        
        if len(full_response) > 0:
            print("✅ SearchService测试成功")
            return True
        else:
            print("❌ SearchService返回空响应")
            return False
            
    except Exception as e:
        print(f"❌ SearchService测试失败: {e}")
        traceback.print_exc()
        return False

def test_api_endpoint():
    """测试API端点"""
    print("\n" + "=" * 60)
    print("🌐 测试搜索API端点")
    print("=" * 60)
    
    try:
        import requests
        
        # 测试搜索端点
        url = "http://localhost:8000/api/search"
        
        payload = {
            "query": "机器学习算法",
            "user_id": 1
        }
        
        print(f"📡 请求URL: {url}")
        print(f"📝 请求数据: {payload}")
        
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API端点测试成功")
            print(f"📄 响应内容长度: {len(response.text)} 字符")
            
            # 显示响应的前200个字符
            preview = response.text[:200]
            print(f"📄 响应预览: {preview}...")
            
            return True
        else:
            print(f"❌ API端点测试失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        traceback.print_exc()
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n" + "=" * 60)
    print("⚙️ 测试配置加载")
    print("=" * 60)
    
    try:
        from app.core.config import settings
        
        print(f"🔑 SERPAPI_KEY: {settings.SERPAPI_KEY[:20]}..." if settings.SERPAPI_KEY else "❌ SERPAPI_KEY未设置")
        print(f"📊 SEARCH_RESULT_COUNT: {settings.SEARCH_RESULT_COUNT}")
        
        if settings.SERPAPI_KEY and len(settings.SERPAPI_KEY) > 20:
            print("✅ SerpAPI配置加载成功")
            return True
        else:
            print("❌ SerpAPI配置加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 SerpAPI功能测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now()}")
    print("=" * 80)
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("配置加载", test_config_loading),
        ("直接SerpAPI测试", test_serpapi_direct),
        ("SearchTool类测试", test_search_tool),
        ("SearchService测试", test_search_service),
        ("API端点测试", test_api_endpoint),
    ]
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 SerpAPI测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed >= 3:
        print("🎉 SerpAPI功能基本正常！")
        print("\n💡 现在可以:")
        print("  1. 在聊天中使用联网搜索功能")
        print("  2. 通过API端点进行搜索")
        print("  3. 集成到LangGraph工作流中")
    else:
        print("⚠️ SerpAPI功能需要进一步检查")
    
    return passed >= 3

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
