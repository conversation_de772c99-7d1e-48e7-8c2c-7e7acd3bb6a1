#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
APOC插件配置验证脚本
帮助诊断和解决APOC配置问题
"""

import sys
import traceback
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "llm_backend"))

def test_apoc_installation_status():
    """测试APOC插件安装状态"""
    print("\n" + "=" * 60)
    print("🔍 检查APOC插件安装状态")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            "bolt://localhost:7687", 
            auth=("neo4j", "12369874c")
        )
        
        with driver.session(database="neo4j") as session:
            # 检查APOC版本
            try:
                result = session.run("CALL apoc.version()")
                record = result.single()
                if record:
                    print(f"✅ APOC插件已安装，版本: {record[0]}")
                else:
                    print("❌ APOC插件未正确安装")
                    return False
            except Exception as e:
                print(f"❌ APOC版本查询失败: {e}")
                return False
            
            # 检查可用的APOC过程
            try:
                result = session.run("CALL apoc.help('') YIELD name RETURN count(name) as count")
                record = result.single()
                print(f"✅ 可用的APOC过程数量: {record['count']}")
            except Exception as e:
                print(f"⚠️ APOC帮助查询失败: {e}")
            
            # 测试基本APOC功能
            try:
                result = session.run("CALL apoc.date.format(timestamp(), 'yyyy-MM-dd')")
                record = result.single()
                print(f"✅ APOC基本功能正常: {record[0]}")
            except Exception as e:
                print(f"❌ APOC基本功能失败: {e}")
            
            # 测试问题的apoc.meta.data
            try:
                result = session.run("CALL apoc.meta.data()")
                records = list(result)
                print(f"✅ apoc.meta.data 执行成功，返回 {len(records)} 条记录")
                return True
            except Exception as e:
                print(f"❌ apoc.meta.data 执行失败: {e}")
                if "sandboxed" in str(e):
                    print("💡 这是安全配置问题，需要配置unrestricted设置")
                return False
        
        driver.close()
        
    except Exception as e:
        print(f"❌ APOC安装状态检查失败: {e}")
        traceback.print_exc()
        return False

def provide_apoc_config_solution():
    """提供APOC配置解决方案"""
    print("\n" + "=" * 60)
    print("🔧 APOC配置解决方案")
    print("=" * 60)
    
    solution = """
🚨 APOC插件安全配置问题解决方案：

方法1：Neo4j Desktop配置（推荐）
1. 停止Neo4j实例 'assistgen'
2. 点击实例旁边的 "..." 按钮
3. 选择 "Settings" 或 "设置"
4. 在配置文件中添加以下行：
   
   dbms.security.procedures.unrestricted=apoc.*
   dbms.security.procedures.allowlist=apoc.*
   
5. 保存配置文件
6. 重新启动Neo4j实例

方法2：手动编辑配置文件
1. 找到Neo4j配置文件 neo4j.conf
2. 添加上述配置行
3. 重启Neo4j服务

方法3：临时解决方案
- 使用我们已经创建的自定义Neo4j连接类
- 完全绕过APOC依赖

配置完成后，重新运行测试验证。
    """
    
    print(solution)
    
    # 保存解决方案到文件
    solution_file = Path("apoc_config_solution.txt")
    with open(solution_file, 'w', encoding='utf-8') as f:
        f.write(solution)
    
    print(f"✅ 解决方案已保存到: {solution_file}")
    return True

def test_langchain_with_apoc_fix():
    """测试配置APOC后的LangChain连接"""
    print("\n" + "=" * 60)
    print("🔗 测试配置APOC后的LangChain连接")
    print("=" * 60)
    
    try:
        from langchain_neo4j import Neo4jGraph
        
        # 尝试使用原始LangChain Neo4j连接
        neo4j_graph = Neo4jGraph(
            url="bolt://localhost:7687",
            username="neo4j",
            password="12369874c",
            database="neo4j"
        )
        
        print("✅ LangChain Neo4j连接成功（APOC已配置）")
        
        # 测试查询
        result = neo4j_graph.query("RETURN 'APOC配置成功!' as message")
        print(f"📝 查询结果: {result}")
        
        # 测试schema获取
        try:
            schema = neo4j_graph.get_schema
            print(f"📋 Schema获取成功: {len(schema)} 字符")
        except Exception as e:
            print(f"⚠️ Schema获取失败: {e}")
        
        print("✅ LangChain Neo4j测试成功（APOC已修复）!")
        return True
        
    except Exception as e:
        print(f"❌ LangChain Neo4j测试失败: {e}")
        if "sandboxed" in str(e):
            print("💡 仍然是安全配置问题，请按照解决方案配置")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 APOC插件配置验证")
    print("=" * 80)
    print("🔍 诊断APOC插件配置问题")
    print("=" * 80)
    
    # 检查APOC安装状态
    apoc_status = test_apoc_installation_status()
    
    # 提供配置解决方案
    provide_apoc_config_solution()
    
    # 如果APOC配置正确，测试LangChain
    if apoc_status:
        test_langchain_with_apoc_fix()
        print("\n🎉 APOC插件配置正确，所有功能正常！")
    else:
        print("\n⚠️ APOC插件需要配置安全设置")
        print("📋 请按照上述解决方案进行配置")
        print("🔄 配置完成后重新运行此脚本验证")
    
    return apoc_status

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
