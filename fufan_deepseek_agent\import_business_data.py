#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
业务数据导入脚本
将CSV数据导入到Neo4j数据库中
"""

import sys
import os
import traceback
import pandas as pd
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "llm_backend"))

def import_categories():
    """导入类别数据"""
    print("\n" + "=" * 50)
    print("📂 导入类别数据")
    print("=" * 50)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        
        # 读取类别数据
        categories_file = Path("llm_backend/app/graphrag/origin_data/exported_data/categories.csv")
        if not categories_file.exists():
            print(f"❌ 类别数据文件不存在: {categories_file}")
            return False
        
        df = pd.read_csv(categories_file)
        print(f"📊 读取到 {len(df)} 个类别")
        
        # 批量导入类别
        for _, row in df.iterrows():
            result = neo4j_graph.query("""
                MERGE (c:Category {id: $id})
                SET c.name = $name,
                    c.description = $description,
                    c.updated_at = datetime()
                RETURN c.name as name
            """, {
                "id": str(row['CategoryID']),
                "name": row['CategoryName'],
                "description": row.get('Description', '')
            })
            
        print(f"✅ 成功导入 {len(df)} 个类别")
        return True
        
    except Exception as e:
        print(f"❌ 类别数据导入失败: {e}")
        traceback.print_exc()
        return False

def import_suppliers():
    """导入供应商数据"""
    print("\n" + "=" * 50)
    print("🏭 导入供应商数据")
    print("=" * 50)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        
        # 读取供应商数据
        suppliers_file = Path("llm_backend/app/graphrag/origin_data/exported_data/suppliers.csv")
        if not suppliers_file.exists():
            print(f"❌ 供应商数据文件不存在: {suppliers_file}")
            return False
        
        df = pd.read_csv(suppliers_file)
        print(f"📊 读取到 {len(df)} 个供应商")
        
        # 批量导入供应商
        for _, row in df.iterrows():
            result = neo4j_graph.query("""
                MERGE (s:Supplier {id: $id})
                SET s.name = $name,
                    s.contact_name = $contact_name,
                    s.contact_title = $contact_title,
                    s.address = $address,
                    s.city = $city,
                    s.country = $country,
                    s.phone = $phone,
                    s.updated_at = datetime()
                RETURN s.name as name
            """, {
                "id": str(row['SupplierID']),
                "name": row['CompanyName'],
                "contact_name": row.get('ContactName', ''),
                "contact_title": row.get('ContactTitle', ''),
                "address": row.get('Address', ''),
                "city": row.get('City', ''),
                "country": row.get('Country', ''),
                "phone": row.get('Phone', '')
            })
            
        print(f"✅ 成功导入 {len(df)} 个供应商")
        return True
        
    except Exception as e:
        print(f"❌ 供应商数据导入失败: {e}")
        traceback.print_exc()
        return False

def import_products():
    """导入产品数据"""
    print("\n" + "=" * 50)
    print("📦 导入产品数据")
    print("=" * 50)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        
        # 读取产品数据
        products_file = Path("llm_backend/app/graphrag/origin_data/exported_data/products.csv")
        if not products_file.exists():
            print(f"❌ 产品数据文件不存在: {products_file}")
            return False
        
        df = pd.read_csv(products_file)
        print(f"📊 读取到 {len(df)} 个产品")
        
        # 批量导入产品
        for _, row in df.iterrows():
            result = neo4j_graph.query("""
                MERGE (p:Product {id: $id})
                SET p.name = $name,
                    p.unit_price = $unit_price,
                    p.units_in_stock = $units_in_stock,
                    p.units_on_order = $units_on_order,
                    p.reorder_level = $reorder_level,
                    p.discontinued = $discontinued,
                    p.updated_at = datetime()
                RETURN p.name as name
            """, {
                "id": str(row['ProductID']),
                "name": row['ProductName'],
                "unit_price": float(row.get('UnitPrice', 0)),
                "units_in_stock": int(row.get('UnitsInStock', 0)),
                "units_on_order": int(row.get('UnitsOnOrder', 0)),
                "reorder_level": int(row.get('ReorderLevel', 0)),
                "discontinued": bool(row.get('Discontinued', False))
            })
            
        print(f"✅ 成功导入 {len(df)} 个产品")
        return True
        
    except Exception as e:
        print(f"❌ 产品数据导入失败: {e}")
        traceback.print_exc()
        return False

def import_customers():
    """导入客户数据"""
    print("\n" + "=" * 50)
    print("👥 导入客户数据")
    print("=" * 50)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        
        # 读取客户数据
        customers_file = Path("llm_backend/app/graphrag/origin_data/exported_data/customers.csv")
        if not customers_file.exists():
            print(f"❌ 客户数据文件不存在: {customers_file}")
            return False
        
        df = pd.read_csv(customers_file)
        print(f"📊 读取到 {len(df)} 个客户")
        
        # 批量导入客户
        for _, row in df.iterrows():
            result = neo4j_graph.query("""
                MERGE (c:Customer {id: $id})
                SET c.name = $name,
                    c.contact_name = $contact_name,
                    c.contact_title = $contact_title,
                    c.address = $address,
                    c.city = $city,
                    c.country = $country,
                    c.phone = $phone,
                    c.updated_at = datetime()
                RETURN c.name as name
            """, {
                "id": str(row['CustomerID']),
                "name": row['CompanyName'],
                "contact_name": row.get('ContactName', ''),
                "contact_title": row.get('ContactTitle', ''),
                "address": row.get('Address', ''),
                "city": row.get('City', ''),
                "country": row.get('Country', ''),
                "phone": row.get('Phone', '')
            })
            
        print(f"✅ 成功导入 {len(df)} 个客户")
        return True
        
    except Exception as e:
        print(f"❌ 客户数据导入失败: {e}")
        traceback.print_exc()
        return False

def create_relationships():
    """创建关系"""
    print("\n" + "=" * 50)
    print("🔗 创建关系")
    print("=" * 50)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        
        # 创建产品-类别关系
        products_file = Path("llm_backend/app/graphrag/origin_data/exported_data/products.csv")
        if products_file.exists():
            df = pd.read_csv(products_file)
            
            for _, row in df.iterrows():
                if pd.notna(row.get('CategoryID')):
                    neo4j_graph.query("""
                        MATCH (p:Product {id: $product_id})
                        MATCH (c:Category {id: $category_id})
                        MERGE (p)-[:BELONGS_TO]->(c)
                    """, {
                        "product_id": str(row['ProductID']),
                        "category_id": str(row['CategoryID'])
                    })
            
            print("✅ 创建产品-类别关系")
        
        # 创建产品-供应商关系
        if products_file.exists():
            df = pd.read_csv(products_file)
            
            for _, row in df.iterrows():
                if pd.notna(row.get('SupplierID')):
                    neo4j_graph.query("""
                        MATCH (p:Product {id: $product_id})
                        MATCH (s:Supplier {id: $supplier_id})
                        MERGE (p)-[:SUPPLIED_BY]->(s)
                    """, {
                        "product_id": str(row['ProductID']),
                        "supplier_id": str(row['SupplierID'])
                    })
            
            print("✅ 创建产品-供应商关系")
        
        return True
        
    except Exception as e:
        print(f"❌ 关系创建失败: {e}")
        traceback.print_exc()
        return False

def verify_import():
    """验证导入结果"""
    print("\n" + "=" * 50)
    print("🔍 验证导入结果")
    print("=" * 50)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        
        # 统计节点数量
        result = neo4j_graph.query("""
            MATCH (n)
            RETURN labels(n)[0] as label, count(n) as count
            ORDER BY count DESC
        """)
        
        print("📊 节点统计:")
        total_nodes = 0
        for record in result:
            count = record['count']
            total_nodes += count
            print(f"  {record['label']}: {count:,} 个")
        
        print(f"\n📈 总节点数: {total_nodes:,}")
        
        # 统计关系数量
        result = neo4j_graph.query("""
            MATCH ()-[r]->()
            RETURN type(r) as type, count(r) as count
            ORDER BY count DESC
        """)
        
        print("\n🔗 关系统计:")
        total_rels = 0
        for record in result:
            count = record['count']
            total_rels += count
            print(f"  {record['type']}: {count:,} 个")
        
        print(f"\n📈 总关系数: {total_rels:,}")
        
        # 测试查询
        result = neo4j_graph.query("""
            MATCH (p:Product)-[:BELONGS_TO]->(c:Category)
            RETURN c.name as category, count(p) as product_count
            ORDER BY product_count DESC
            LIMIT 5
        """)
        
        if result:
            print("\n🏆 热门类别:")
            for record in result:
                print(f"  {record['category']}: {record['product_count']} 个产品")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主导入函数"""
    print("🚀 业务数据导入")
    print("=" * 80)
    print(f"📅 开始时间: {datetime.now()}")
    print("=" * 80)
    
    # 清理现有数据
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        neo4j_graph = get_neo4j_graph()
        neo4j_graph.query("MATCH (n) DETACH DELETE n")
        print("🧹 清理现有数据完成")
    except Exception as e:
        print(f"⚠️ 清理数据失败: {e}")
    
    # 执行导入步骤
    steps = [
        ("导入类别数据", import_categories),
        ("导入供应商数据", import_suppliers),
        ("导入产品数据", import_products),
        ("导入客户数据", import_customers),
        ("创建关系", create_relationships),
        ("验证导入结果", verify_import),
    ]
    
    results = []
    for step_name, step_func in steps:
        try:
            result = step_func()
            results.append((step_name, result))
            if not result:
                print(f"⚠️ {step_name} 失败，继续下一步...")
        except Exception as e:
            print(f"❌ {step_name} 执行异常: {e}")
            results.append((step_name, False))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 业务数据导入结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for step_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{status} {step_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 步骤成功")
    print(f"🕐 完成时间: {datetime.now()}")
    
    if passed >= 4:
        print("\n🎉 业务数据导入基本成功！")
        print("💡 现在可以开始测试图查询和AI Agent功能")
    else:
        print("\n⚠️ 业务数据导入部分失败，请检查数据文件")
    
    return passed >= 4

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
