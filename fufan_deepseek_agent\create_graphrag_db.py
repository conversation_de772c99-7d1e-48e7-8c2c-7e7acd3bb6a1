#!/usr/bin/env python3
"""
创建GraphRAG数据库
"""

import pymysql

# 数据库连接信息（不指定数据库）
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '123456',
    'charset': 'utf8mb4'
}

def create_graphrag_database():
    """创建GraphRAG数据库"""
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ MySQL连接成功!")
        
        with connection.cursor() as cursor:
            # 检查数据库是否存在
            cursor.execute("SHOW DATABASES LIKE 'graphrag'")
            exists = cursor.fetchone()
            
            if exists:
                print("✅ 数据库 'graphrag' 已存在")
            else:
                # 创建数据库
                cursor.execute("CREATE DATABASE graphrag CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                print("✅ 数据库 'graphrag' 创建成功")
            
            connection.commit()
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        return False

if __name__ == "__main__":
    create_graphrag_database()
