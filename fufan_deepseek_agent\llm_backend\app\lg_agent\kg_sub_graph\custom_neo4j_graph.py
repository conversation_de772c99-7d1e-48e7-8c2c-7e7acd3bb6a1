"""
自定义Neo4j图连接类
完全绕过APOC依赖，提供基本的Neo4j图数据库操作功能
"""

from neo4j import GraphDatabase
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class CustomNeo4jGraph:
    """
    自定义Neo4j图连接类，不依赖APOC插件
    提供基本的图数据库操作功能
    """
    
    def __init__(
        self,
        url: str,
        username: str,
        password: str,
        database: str = "neo4j"
    ):
        """
        初始化Neo4j连接
        
        Args:
            url: Neo4j连接URL
            username: 用户名
            password: 密码
            database: 数据库名称
        """
        self.url = url
        self.username = username
        self.password = password
        self.database = database
        
        # 创建驱动
        self._driver = GraphDatabase.driver(
            url, 
            auth=(username, password)
        )
        
        # 验证连接
        self._verify_connectivity()
        
        # 初始化schema信息
        self._schema = self._get_simple_schema()
    
    def _verify_connectivity(self):
        """验证数据库连接"""
        try:
            with self._driver.session(database=self.database) as session:
                session.run("RETURN 1")
            logger.info(f"Successfully connected to Neo4j at {self.url}")
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise
    
    def _get_simple_schema(self) -> Dict[str, Any]:
        """
        获取简化的数据库schema信息（不依赖APOC）
        
        Returns:
            包含节点标签、关系类型和属性键的字典
        """
        schema = {
            "node_labels": [],
            "relationship_types": [],
            "property_keys": []
        }
        
        try:
            with self._driver.session(database=self.database) as session:
                # 获取节点标签
                result = session.run("CALL db.labels()")
                schema["node_labels"] = [record[0] for record in result]
                
                # 获取关系类型
                result = session.run("CALL db.relationshipTypes()")
                schema["relationship_types"] = [record[0] for record in result]
                
                # 获取属性键
                result = session.run("CALL db.propertyKeys()")
                schema["property_keys"] = [record[0] for record in result]
                
        except Exception as e:
            logger.warning(f"Failed to get schema: {e}")
        
        return schema
    
    def query(self, cypher: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行Cypher查询
        
        Args:
            cypher: Cypher查询语句
            params: 查询参数
            
        Returns:
            查询结果列表
        """
        if params is None:
            params = {}
        
        try:
            with self._driver.session(database=self.database) as session:
                result = session.run(cypher, params)
                return [record.data() for record in result]
        except Exception as e:
            logger.error(f"Query failed: {e}")
            raise
    
    def execute_write(self, cypher: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行写操作
        
        Args:
            cypher: Cypher写操作语句
            params: 查询参数
            
        Returns:
            操作结果列表
        """
        if params is None:
            params = {}
        
        try:
            with self._driver.session(database=self.database) as session:
                result = session.run(cypher, params)
                return [record.data() for record in result]
        except Exception as e:
            logger.error(f"Write operation failed: {e}")
            raise
    
    def refresh_schema(self):
        """刷新schema信息"""
        self._schema = self._get_simple_schema()
        logger.info("Schema refreshed")
    
    @property
    def get_schema(self) -> str:
        """
        获取schema的字符串表示
        
        Returns:
            Schema的文本描述
        """
        schema_text = "Neo4j Database Schema:\n"
        schema_text += f"Node Labels: {', '.join(self._schema['node_labels'])}\n"
        schema_text += f"Relationship Types: {', '.join(self._schema['relationship_types'])}\n"
        schema_text += f"Property Keys: {', '.join(self._schema['property_keys'])}\n"
        return schema_text
    
    @property
    def structured_schema(self) -> Dict[str, Any]:
        """
        获取结构化的schema信息
        
        Returns:
            结构化的schema字典
        """
        return self._schema.copy()
    
    def get_node_count(self) -> int:
        """获取节点总数"""
        result = self.query("MATCH (n) RETURN count(n) as count")
        return result[0]["count"] if result else 0
    
    def get_relationship_count(self) -> int:
        """获取关系总数"""
        result = self.query("MATCH ()-[r]->() RETURN count(r) as count")
        return result[0]["count"] if result else 0
    
    def get_database_info(self) -> Dict[str, Any]:
        """
        获取数据库基本信息
        
        Returns:
            包含数据库信息的字典
        """
        info = {
            "url": self.url,
            "database": self.database,
            "node_count": self.get_node_count(),
            "relationship_count": self.get_relationship_count(),
            "schema": self._schema
        }
        return info
    
    def close(self):
        """关闭数据库连接"""
        if self._driver:
            self._driver.close()
            logger.info("Neo4j connection closed")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
    
    def __del__(self):
        """析构函数"""
        try:
            self.close()
        except:
            pass
