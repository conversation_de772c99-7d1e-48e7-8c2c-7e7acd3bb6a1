#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
知识图谱查询测试脚本
测试各种复杂的图查询功能
"""

import sys
import traceback
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "llm_backend"))

def test_basic_graph_queries():
    """测试基本图查询"""
    print("\n" + "=" * 60)
    print("🔍 测试基本图查询")
    print("=" * 60)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        
        # 查询所有节点类型和数量
        result = neo4j_graph.query("""
            MATCH (n)
            RETURN labels(n)[0] as node_type, count(n) as count
            ORDER BY count DESC
        """)
        
        print("📊 节点类型统计:")
        for record in result:
            print(f"  {record['node_type']}: {record['count']:,} 个")
        
        # 查询所有关系类型和数量
        result = neo4j_graph.query("""
            MATCH ()-[r]->()
            RETURN type(r) as relationship_type, count(r) as count
            ORDER BY count DESC
        """)
        
        print("\n🔗 关系类型统计:")
        for record in result:
            print(f"  {record['relationship_type']}: {record['count']:,} 个")
        
        print("✅ 基本图查询测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 基本图查询测试失败: {e}")
        traceback.print_exc()
        return False

def test_product_category_analysis():
    """测试产品类别分析"""
    print("\n" + "=" * 60)
    print("📦 测试产品类别分析")
    print("=" * 60)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        
        # 分析每个类别的产品数量和价格统计
        result = neo4j_graph.query("""
            MATCH (p:Product)-[:BELONGS_TO]->(c:Category)
            RETURN c.name as category,
                   count(p) as product_count,
                   avg(p.unit_price) as avg_price,
                   max(p.unit_price) as max_price,
                   min(p.unit_price) as min_price,
                   sum(p.units_in_stock) as total_stock
            ORDER BY product_count DESC
        """)
        
        if result:
            print("📊 类别分析结果:")
            for record in result:
                print(f"  📂 {record['category']}:")
                print(f"    产品数量: {record['product_count']}")
                print(f"    平均价格: ${record['avg_price']:.2f}")
                print(f"    价格区间: ${record['min_price']:.2f} - ${record['max_price']:.2f}")
                print(f"    总库存: {record['total_stock']}")
                print()
        else:
            print("⚠️ 没有找到产品类别数据")
        
        # 查找最贵的产品
        result = neo4j_graph.query("""
            MATCH (p:Product)-[:BELONGS_TO]->(c:Category)
            RETURN p.name as product, c.name as category, p.unit_price as price
            ORDER BY p.unit_price DESC
            LIMIT 5
        """)
        
        if result:
            print("💰 最贵的5个产品:")
            for i, record in enumerate(result, 1):
                print(f"  {i}. {record['product']} ({record['category']}) - ${record['price']:.2f}")
        
        print("✅ 产品类别分析测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 产品类别分析测试失败: {e}")
        traceback.print_exc()
        return False

def test_supplier_analysis():
    """测试供应商分析"""
    print("\n" + "=" * 60)
    print("🏭 测试供应商分析")
    print("=" * 60)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        
        # 分析供应商提供的产品数量
        result = neo4j_graph.query("""
            MATCH (p:Product)-[:SUPPLIED_BY]->(s:Supplier)
            RETURN s.name as supplier,
                   s.country as country,
                   count(p) as product_count,
                   avg(p.unit_price) as avg_price
            ORDER BY product_count DESC
            LIMIT 10
        """)
        
        if result:
            print("🏭 供应商产品统计:")
            for record in result:
                print(f"  🏢 {record['supplier']} ({record['country']})")
                print(f"    产品数量: {record['product_count']}")
                print(f"    平均价格: ${record['avg_price']:.2f}")
                print()
        else:
            print("⚠️ 没有找到供应商数据")
        
        # 查找跨类别供应商
        result = neo4j_graph.query("""
            MATCH (s:Supplier)<-[:SUPPLIED_BY]-(p:Product)-[:BELONGS_TO]->(c:Category)
            WITH s, collect(DISTINCT c.name) as categories
            WHERE size(categories) > 1
            RETURN s.name as supplier, categories, size(categories) as category_count
            ORDER BY category_count DESC
            LIMIT 5
        """)
        
        if result:
            print("🔄 跨类别供应商:")
            for record in result:
                print(f"  🏢 {record['supplier']}: {record['category_count']} 个类别")
                print(f"    类别: {', '.join(record['categories'])}")
                print()
        
        print("✅ 供应商分析测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 供应商分析测试失败: {e}")
        traceback.print_exc()
        return False

def test_path_queries():
    """测试路径查询"""
    print("\n" + "=" * 60)
    print("🛤️ 测试路径查询")
    print("=" * 60)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        
        # 查找产品到供应商的路径
        result = neo4j_graph.query("""
            MATCH path = (p:Product)-[:SUPPLIED_BY]->(s:Supplier)
            RETURN p.name as product, s.name as supplier, s.country as country
            LIMIT 5
        """)
        
        if result:
            print("🛤️ 产品-供应商路径:")
            for record in result:
                print(f"  📦 {record['product']} ← 🏢 {record['supplier']} ({record['country']})")
        
        # 查找产品到类别的路径
        result = neo4j_graph.query("""
            MATCH path = (p:Product)-[:BELONGS_TO]->(c:Category)
            RETURN p.name as product, c.name as category
            LIMIT 5
        """)
        
        if result:
            print("\n📂 产品-类别路径:")
            for record in result:
                print(f"  📦 {record['product']} → 📂 {record['category']}")
        
        # 查找复杂路径：供应商-产品-类别
        result = neo4j_graph.query("""
            MATCH path = (s:Supplier)<-[:SUPPLIED_BY]-(p:Product)-[:BELONGS_TO]->(c:Category)
            RETURN s.name as supplier, p.name as product, c.name as category
            LIMIT 5
        """)
        
        if result:
            print("\n🔗 供应商-产品-类别路径:")
            for record in result:
                print(f"  🏢 {record['supplier']} → 📦 {record['product']} → 📂 {record['category']}")
        
        print("✅ 路径查询测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 路径查询测试失败: {e}")
        traceback.print_exc()
        return False

def test_aggregation_queries():
    """测试聚合查询"""
    print("\n" + "=" * 60)
    print("📊 测试聚合查询")
    print("=" * 60)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        
        # 全局统计
        result = neo4j_graph.query("""
            MATCH (p:Product)
            RETURN count(p) as total_products,
                   avg(p.unit_price) as avg_price,
                   sum(p.units_in_stock) as total_stock,
                   max(p.unit_price) as max_price,
                   min(p.unit_price) as min_price
        """)
        
        if result:
            record = result[0]
            print("🌍 全局产品统计:")
            print(f"  总产品数: {record['total_products']:,}")
            print(f"  平均价格: ${record['avg_price']:.2f}")
            print(f"  总库存: {record['total_stock']:,}")
            print(f"  价格区间: ${record['min_price']:.2f} - ${record['max_price']:.2f}")
        
        # 按国家统计供应商
        result = neo4j_graph.query("""
            MATCH (s:Supplier)
            RETURN s.country as country, count(s) as supplier_count
            ORDER BY supplier_count DESC
            LIMIT 10
        """)
        
        if result:
            print("\n🌍 按国家统计供应商:")
            for record in result:
                print(f"  {record['country']}: {record['supplier_count']} 个供应商")
        
        # 库存预警分析
        result = neo4j_graph.query("""
            MATCH (p:Product)
            WHERE p.units_in_stock <= p.reorder_level
            RETURN count(p) as low_stock_products
        """)
        
        if result:
            low_stock = result[0]['low_stock_products']
            print(f"\n⚠️ 库存预警: {low_stock} 个产品库存不足")
        
        print("✅ 聚合查询测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 聚合查询测试失败: {e}")
        traceback.print_exc()
        return False

def test_graph_algorithms():
    """测试图算法"""
    print("\n" + "=" * 60)
    print("🧮 测试图算法")
    print("=" * 60)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        
        # 计算节点度数
        result = neo4j_graph.query("""
            MATCH (n)
            OPTIONAL MATCH (n)-[r]-()
            RETURN labels(n)[0] as node_type, 
                   n.name as name,
                   count(r) as degree
            ORDER BY degree DESC
            LIMIT 10
        """)
        
        if result:
            print("📊 节点度数排名:")
            for record in result:
                print(f"  {record['node_type']}: {record['name']} (度数: {record['degree']})")
        
        # 查找孤立节点
        result = neo4j_graph.query("""
            MATCH (n)
            WHERE NOT (n)-[]-()
            RETURN labels(n)[0] as node_type, count(n) as isolated_count
        """)
        
        if result:
            print("\n🏝️ 孤立节点统计:")
            for record in result:
                if record['isolated_count'] > 0:
                    print(f"  {record['node_type']}: {record['isolated_count']} 个孤立节点")
        
        # 查找连通组件大小
        result = neo4j_graph.query("""
            MATCH (n)
            RETURN count(n) as total_nodes
        """)
        
        if result:
            total_nodes = result[0]['total_nodes']
            print(f"\n🔗 图连通性: 总共 {total_nodes} 个节点")
        
        print("✅ 图算法测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 图算法测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 知识图谱查询测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now()}")
    print("=" * 80)
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("基本图查询", test_basic_graph_queries),
        ("产品类别分析", test_product_category_analysis),
        ("供应商分析", test_supplier_analysis),
        ("路径查询", test_path_queries),
        ("聚合查询", test_aggregation_queries),
        ("图算法", test_graph_algorithms),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 知识图谱查询测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 知识图谱查询功能完全正常！")
        print("\n💡 图数据库已准备好用于:")
        print("  1. 复杂的业务分析查询")
        print("  2. AI Agent知识检索")
        print("  3. 图算法和推荐系统")
        print("  4. 实时业务洞察")
    elif passed >= 4:
        print("🎯 知识图谱查询基本功能正常！")
        print("💡 可以开始集成到AI Agent中")
    else:
        print("⚠️ 知识图谱查询功能需要进一步完善")
    
    return passed >= 4

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
