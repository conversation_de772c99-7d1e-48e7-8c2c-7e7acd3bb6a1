# 🚀 **项目部署完整指南 - 从零开始**

## 📋 **系统要求**

### **必需软件**
- **Python 3.8+** (推荐 3.10 或 3.11)
- **Neo4j Desktop** (最新版本)
- **MySQL 8.0+** 
- **Git** (用于克隆代码)

### **硬件要求**
- **内存**: 至少 8GB RAM (推荐 16GB)
- **存储**: 至少 10GB 可用空间
- **网络**: 稳定的互联网连接

---

## 🔧 **第一步：环境准备**

### **1.1 安装Python**
```bash
# 下载并安装 Python 3.10+ 
# 官网：https://www.python.org/downloads/
# 安装时勾选 "Add Python to PATH"
```

### **1.2 安装Neo4j Desktop**
```bash
# 下载地址：https://neo4j.com/download/
# 1. 下载并安装 Neo4j Desktop
# 2. 创建账户并登录
# 3. 创建新项目，命名为 "assistgen"
```

### **1.3 安装MySQL**
```bash
# 下载地址：https://dev.mysql.com/downloads/mysql/
# 安装时记住root密码
```

---

## 📁 **第二步：获取项目代码**

### **2.1 克隆项目**
```bash
# 在合适的目录下执行
git clone [项目地址]
cd 004-asssgen/fufan_deepseek_agent
```

### **2.2 安装Python依赖**
```bash
# 进入后端目录
cd llm_backend

# 安装依赖
pip install -r requirements.txt

# 如果安装失败，尝试：
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

---

## ⚙️ **第三步：配置环境变量**

### **3.1 复制环境变量文件**
```bash
# 在 llm_backend 目录下
cp .env.example .env
# 或者手动创建 .env 文件
```

### **3.2 编辑 .env 文件**
```bash
# 必须配置的关键参数：

# DeepSeek API配置 (必需)
DEEPSEEK_API_KEY=你的DeepSeek_API密钥
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_MODEL=deepseek-chat

# Vision模型配置 (必需)
VISION_API_KEY=你的DeepSeek_API密钥
VISION_BASE_URL=https://api.deepseek.com
VISION_MODEL=deepseek-vl

# Ollama配置 (本地模型，可选)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_CHAT_MODEL=qwen2.5:7b
OLLAMA_REASON_MODEL=qwen2.5:7b
OLLAMA_EMBEDDING_MODEL=nomic-embed-text
OLLAMA_AGENT_MODEL=qwen2.5:7b

# 服务选择
CHAT_SERVICE=deepseek
REASON_SERVICE=deepseek
AGENT_SERVICE=deepseek

# SerpAPI配置 (联网搜索)
SERPAPI_KEY=你的SerpAPI密钥
SEARCH_RESULT_COUNT=10

# MySQL数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=你的MySQL密码
DB_NAME=assistgen

# Neo4j配置
NEO4J_URL=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=你设置的Neo4j密码
NEO4J_DATABASE=neo4j
```

---

## 🗄️ **第四步：数据库配置**

### **4.1 配置MySQL**
```bash
# 1. 启动MySQL服务
# 2. 创建数据库
mysql -u root -p
CREATE DATABASE assistgen CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
exit;

# 3. 初始化数据库表
cd llm_backend
python scripts/init_db.py
```

### **4.2 配置Neo4j**
```bash
# 1. 打开Neo4j Desktop
# 2. 创建新数据库实例：
#    - 名称：assistgen
#    - 密码：设置一个密码（记住，要填入.env文件）
#    - 版本：选择最新版本

# 3. 安装APOC插件：
#    - 点击数据库实例
#    - 进入"Plugins"标签
#    - 安装"APOC"插件
#    - 确保版本匹配

# 4. 启动数据库实例
```

---

## 🔑 **第五步：获取API密钥**

### **5.1 DeepSeek API密钥**
```bash
# 1. 访问：https://platform.deepseek.com/
# 2. 注册账户并登录
# 3. 进入API Keys页面
# 4. 创建新的API密钥
# 5. 复制密钥到.env文件
```

### **5.2 SerpAPI密钥 (可选)**
```bash
# 1. 访问：https://serpapi.com/
# 2. 注册账户
# 3. 获取API密钥
# 4. 复制到.env文件
```

---

## 📊 **第六步：导入业务数据**

### **6.1 导入Neo4j数据**
```bash
# 在项目根目录下执行
python import_business_data.py
```

### **6.2 验证数据导入**
```bash
# 运行测试脚本
python test_knowledge_graph_queries.py
```

---

## 🚀 **第七步：启动项目**

### **7.1 启动后端服务**
```bash
# 在 llm_backend 目录下
python run.py

# 看到以下信息表示启动成功：
# INFO:     Uvicorn running on http://0.0.0.0:8000
```

### **7.2 验证服务**
```bash
# 打开浏览器访问：
http://localhost:8000/docs

# 应该看到Swagger API文档界面
```

---

## ✅ **第八步：功能测试**

### **8.1 运行综合测试**
```bash
# 测试Neo4j连接
python test_neo4j_comprehensive.py

# 测试SerpAPI
python test_serpapi.py

# 测试知识图谱
python test_knowledge_graph_queries.py
```

### **8.2 测试API端点**
```bash
# 在Swagger界面测试：
# 1. GET /health - 健康检查
# 2. POST /api/langgraph/query - AI Agent查询
# 3. POST /api/search - 搜索功能
```

---

## 🎯 **常见问题解决**

### **问题1：Python依赖安装失败**
```bash
# 解决方案：
pip install --upgrade pip
pip install -r requirements.txt --no-cache-dir
```

### **问题2：Neo4j连接失败**
```bash
# 检查：
# 1. Neo4j实例是否启动
# 2. 密码是否正确
# 3. APOC插件是否安装
```

### **问题3：MySQL连接失败**
```bash
# 检查：
# 1. MySQL服务是否启动
# 2. 数据库是否创建
# 3. 用户名密码是否正确
```

### **问题4：API密钥无效**
```bash
# 检查：
# 1. DeepSeek API密钥是否正确
# 2. 账户是否有余额
# 3. .env文件是否正确加载
```

---

## 📝 **完整启动检查清单**

### **✅ 环境检查**
- [ ] Python 3.8+ 已安装
- [ ] Neo4j Desktop 已安装并运行
- [ ] MySQL 已安装并运行
- [ ] 项目代码已下载

### **✅ 配置检查**
- [ ] .env 文件已创建并配置
- [ ] DeepSeek API密钥已设置
- [ ] 数据库连接信息已配置
- [ ] SerpAPI密钥已设置（可选）

### **✅ 数据库检查**
- [ ] MySQL数据库已创建
- [ ] 数据库表已初始化
- [ ] Neo4j实例已启动
- [ ] APOC插件已安装
- [ ] 业务数据已导入

### **✅ 服务检查**
- [ ] 后端服务启动成功
- [ ] API文档可访问
- [ ] 健康检查通过
- [ ] 测试脚本运行成功

---

## 🎉 **启动成功标志**

**当看到以下内容时，表示项目部署成功：**

1. **后端服务启动**：`INFO: Uvicorn running on http://0.0.0.0:8000`
2. **API文档可访问**：`http://localhost:8000/docs` 显示完整接口
3. **健康检查通过**：`/health` 返回 `{"status": "ok"}`
4. **数据库连接正常**：测试脚本全部通过
5. **AI功能可用**：可以通过API进行对话和搜索

**🎊 恭喜！项目已成功部署，可以开始使用强大的AI Agent功能了！**
