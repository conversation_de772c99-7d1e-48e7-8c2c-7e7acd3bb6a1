#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Neo4j最终综合测试脚本
验证所有Neo4j功能是否正常工作
"""

import sys
import os
import traceback
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "llm_backend"))

def test_neo4j_basic_connection():
    """测试Neo4j基本连接"""
    print("\n" + "=" * 60)
    print("🔌 测试Neo4j基本连接")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            "bolt://localhost:7687", 
            auth=("neo4j", "12369874c")
        )
        
        with driver.session(database="neo4j") as session:
            result = session.run("RETURN 'Neo4j连接成功!' as message, datetime() as time")
            record = result.single()
            print(f"✅ {record['message']}")
            print(f"🕐 服务器时间: {record['time']}")
            
            # 检查数据库状态
            result = session.run("MATCH (n) RETURN count(n) as node_count")
            node_count = result.single()['node_count']
            print(f"📊 当前节点数量: {node_count}")
        
        driver.close()
        print("✅ Neo4j基本连接测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ Neo4j基本连接测试失败: {e}")
        traceback.print_exc()
        return False

def test_langchain_neo4j_fixed():
    """测试修复后的LangChain Neo4j连接"""
    print("\n" + "=" * 60)
    print("🔗 测试修复后的LangChain Neo4j连接")
    print("=" * 60)
    
    try:
        from langchain_neo4j import Neo4jGraph
        
        # 使用修复后的配置（禁用enhanced_schema）
        neo4j_graph = Neo4jGraph(
            url="bolt://localhost:7687",
            username="neo4j",
            password="12369874c",
            database="neo4j",
            enhanced_schema=False  # 关键修复：禁用增强schema
        )
        
        print("✅ LangChain Neo4j连接成功（已修复APOC问题）")
        
        # 测试基本查询
        result = neo4j_graph.query("RETURN 'LangChain修复测试成功!' as message")
        print(f"📝 查询结果: {result}")
        
        # 测试简化的schema获取
        try:
            schema = neo4j_graph.get_schema
            print(f"📋 简化Schema: {schema}")
        except Exception as e:
            print(f"⚠️ Schema获取失败（正常，数据库为空）: {e}")
        
        print("✅ 修复后的LangChain Neo4j测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 修复后的LangChain Neo4j测试失败: {e}")
        traceback.print_exc()
        return False

def test_project_neo4j_integration_fixed():
    """测试修复后的项目Neo4j集成"""
    print("\n" + "=" * 60)
    print("🏗️ 测试修复后的项目Neo4j集成")
    print("=" * 60)
    
    try:
        # 测试修复后的项目Neo4j连接模块
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        print("📦 导入修复后的项目Neo4j连接模块成功")
        
        # 创建Neo4j图实例
        neo4j_graph = get_neo4j_graph()
        print("✅ 修复后的项目Neo4j图实例创建成功")
        
        # 测试基本查询
        result = neo4j_graph.query("RETURN '项目集成修复测试成功!' as message, datetime() as time")
        print(f"📝 查询结果: {result}")
        
        # 测试数据操作
        result = neo4j_graph.query("""
            CREATE (test:ProjectIntegrationTest {
                name: '项目集成测试节点',
                created_at: datetime(),
                status: 'success'
            })
            RETURN test.name as name
        """)
        print(f"✅ 创建测试节点: {result}")
        
        # 清理测试数据
        neo4j_graph.query("MATCH (n:ProjectIntegrationTest) DELETE n")
        print("🧹 清理测试数据完成")
        
        print("✅ 修复后的项目Neo4j集成测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 修复后的项目Neo4j集成测试失败: {e}")
        traceback.print_exc()
        return False

def test_data_import_readiness():
    """测试数据导入准备情况"""
    print("\n" + "=" * 60)
    print("📊 测试数据导入准备情况")
    print("=" * 60)
    
    try:
        # 检查数据导入脚本
        import_script = Path("llm_backend/app/graphrag/origin_data/create_neo4j_import.py")
        if import_script.exists():
            print(f"✅ 数据导入脚本存在: {import_script}")
            
            # 检查脚本内容
            with open(import_script, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if "neo4j" in content.lower():
                print("✅ 导入脚本包含Neo4j相关代码")
            if "csv" in content.lower():
                print("✅ 导入脚本支持CSV格式")
                
        else:
            print(f"❌ 数据导入脚本不存在: {import_script}")
            return False
        
        # 检查导出数据
        data_dir = Path("llm_backend/app/graphrag/origin_data/exported_data")
        if data_dir.exists():
            csv_files = list(data_dir.glob("*.csv"))
            print(f"✅ 找到 {len(csv_files)} 个CSV数据文件")
            
            # 显示前几个文件
            for i, csv_file in enumerate(csv_files[:5]):
                print(f"  📄 {csv_file.name}")
            if len(csv_files) > 5:
                print(f"  ... 还有 {len(csv_files) - 5} 个文件")
        else:
            print(f"❌ 导出数据目录不存在: {data_dir}")
            return False
        
        print("✅ 数据导入准备情况检查完成!")
        return True
        
    except Exception as e:
        print(f"❌ 数据导入准备情况测试失败: {e}")
        traceback.print_exc()
        return False

def test_neo4j_performance():
    """测试Neo4j性能"""
    print("\n" + "=" * 60)
    print("⚡ 测试Neo4j性能")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        import time
        
        driver = GraphDatabase.driver(
            "bolt://localhost:7687", 
            auth=("neo4j", "12369874c")
        )
        
        with driver.session(database="neo4j") as session:
            # 清理测试数据
            session.run("MATCH (n:PerformanceTest) DETACH DELETE n")
            
            # 批量创建节点性能测试
            start_time = time.time()
            session.run("""
                UNWIND range(1, 1000) as i
                CREATE (n:PerformanceTest {
                    id: i,
                    name: 'Node_' + toString(i),
                    created_at: datetime()
                })
            """)
            create_time = time.time() - start_time
            print(f"✅ 创建1000个节点耗时: {create_time:.3f}秒")
            
            # 查询性能测试
            start_time = time.time()
            result = session.run("MATCH (n:PerformanceTest) RETURN count(n) as count")
            count = result.single()['count']
            query_time = time.time() - start_time
            print(f"✅ 查询{count}个节点耗时: {query_time:.3f}秒")
            
            # 关系创建性能测试
            start_time = time.time()
            session.run("""
                MATCH (n1:PerformanceTest), (n2:PerformanceTest)
                WHERE n1.id = n2.id + 1 AND n1.id <= 100
                CREATE (n1)-[:NEXT]->(n2)
            """)
            rel_time = time.time() - start_time
            print(f"✅ 创建99个关系耗时: {rel_time:.3f}秒")
            
            # 清理测试数据
            session.run("MATCH (n:PerformanceTest) DETACH DELETE n")
            print("🧹 性能测试数据清理完成")
        
        driver.close()
        print("✅ Neo4j性能测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ Neo4j性能测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 Neo4j最终综合测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now()}")
    print("🔧 已修复APOC依赖问题")
    print("=" * 80)
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("Neo4j基本连接", test_neo4j_basic_connection),
        ("修复后的LangChain Neo4j连接", test_langchain_neo4j_fixed),
        ("修复后的项目Neo4j集成", test_project_neo4j_integration_fixed),
        ("数据导入准备情况", test_data_import_readiness),
        ("Neo4j性能测试", test_neo4j_performance),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 最终测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有Neo4j测试都通过了！")
        print("\n🚀 Neo4j环境已完全就绪！")
        print("\n💡 下一步可以进行:")
        print("  1. ✅ 运行数据导入脚本")
        print("  2. ✅ 测试知识图谱查询功能")
        print("  3. ✅ 集成到LangGraph工作流中")
        print("  4. ✅ 开始实际的项目开发")
    elif passed >= 3:
        print("🎯 核心功能测试通过！")
        print("✅ Neo4j环境基本就绪，可以开始使用")
    else:
        print("⚠️ 部分重要测试失败，需要进一步检查")
    
    return passed >= 3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
