#!/usr/bin/env python3
"""
MySQL数据库模型测试脚本
测试项目中的数据库模型和表操作
"""

import os
import sys
import asyncio
from pathlib import Path
from datetime import datetime

# 添加项目路径到sys.path
project_root = Path(__file__).parent
llm_backend_path = project_root / "llm_backend"
sys.path.insert(0, str(llm_backend_path))

# 设置环境变量文件路径
env_file = llm_backend_path / ".env"
os.environ.setdefault("ENV_FILE", str(env_file))

from dotenv import load_dotenv
load_dotenv(env_file)

async def test_database_models():
    """测试数据库模型"""
    print("=" * 60)
    print("🏗️  测试数据库模型")
    print("=" * 60)
    
    try:
        from app.core.database import engine, AsyncSessionLocal
        from app.models.user import User
        from app.models.conversation import Conversation, DialogueType
        from app.models.message import Message
        from sqlalchemy import text
        
        print("✅ 模型导入成功!")
        
        # 测试表创建
        async with engine.begin() as conn:
            # 检查表是否存在
            result = await conn.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result.fetchall()]
            print(f"📋 现有表: {tables}")
            
            # 创建所有表
            from app.core.database import Base
            await conn.run_sync(Base.metadata.create_all)
            print("✅ 表结构创建/更新成功!")
            
            # 再次检查表
            result = await conn.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result.fetchall()]
            print(f"📋 更新后的表: {tables}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_user_operations():
    """测试用户操作"""
    print("\n" + "=" * 60)
    print("👤 测试用户操作")
    print("=" * 60)
    
    try:
        from app.core.database import AsyncSessionLocal
        from app.models.user import User
        from sqlalchemy import select
        
        async with AsyncSessionLocal() as session:
            # 查询现有用户
            result = await session.execute(select(User))
            users = result.scalars().all()
            print(f"📊 现有用户数量: {len(users)}")
            
            for user in users:
                print(f"  - ID: {user.id}, 用户名: {user.username}, 邮箱: {user.email}")
            
            # 创建测试用户（如果不存在）
            test_username = "test_user_mysql"
            result = await session.execute(select(User).where(User.username == test_username))
            existing_user = result.scalar_one_or_none()
            
            if not existing_user:
                new_user = User(
                    username=test_username,
                    email="<EMAIL>",
                    password_hash="test_hash_123",
                    status="active"
                )
                session.add(new_user)
                await session.commit()
                await session.refresh(new_user)
                print(f"✅ 创建测试用户: ID={new_user.id}, 用户名={new_user.username}")
            else:
                print(f"✅ 测试用户已存在: ID={existing_user.id}, 用户名={existing_user.username}")
        
        return True
        
    except Exception as e:
        print(f"❌ 用户操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_conversation_operations():
    """测试对话操作"""
    print("\n" + "=" * 60)
    print("💬 测试对话操作")
    print("=" * 60)
    
    try:
        from app.core.database import AsyncSessionLocal
        from app.models.user import User
        from app.models.conversation import Conversation, DialogueType
        from app.models.message import Message
        from sqlalchemy import select
        
        async with AsyncSessionLocal() as session:
            # 获取测试用户
            result = await session.execute(select(User).where(User.username == "test_user_mysql"))
            test_user = result.scalar_one_or_none()
            
            if not test_user:
                print("❌ 未找到测试用户，请先运行用户操作测试")
                return False
            
            # 查询现有对话
            result = await session.execute(select(Conversation).where(Conversation.user_id == test_user.id))
            conversations = result.scalars().all()
            print(f"📊 用户 {test_user.username} 的对话数量: {len(conversations)}")
            
            # 创建测试对话
            test_conversation = Conversation(
                user_id=test_user.id,
                title="MySQL测试对话",
                dialogue_type=DialogueType.NORMAL
            )
            session.add(test_conversation)
            await session.commit()
            await session.refresh(test_conversation)
            print(f"✅ 创建测试对话: ID={test_conversation.id}, 标题={test_conversation.title}")
            
            # 创建测试消息
            test_message = Message(
                conversation_id=test_conversation.id,
                sender="user",
                content="这是一条MySQL测试消息",
                message_type="text"
            )
            session.add(test_message)
            await session.commit()
            await session.refresh(test_message)
            print(f"✅ 创建测试消息: ID={test_message.id}, 内容={test_message.content[:20]}...")
            
            # 查询对话和消息
            result = await session.execute(
                select(Conversation).where(Conversation.id == test_conversation.id)
            )
            conversation = result.scalar_one()
            
            result = await session.execute(
                select(Message).where(Message.conversation_id == conversation.id)
            )
            messages = result.scalars().all()
            
            print(f"📊 对话 '{conversation.title}' 包含 {len(messages)} 条消息")
            for msg in messages:
                print(f"  - {msg.sender}: {msg.content[:30]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 对话操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_table_structure():
    """测试表结构"""
    print("\n" + "=" * 60)
    print("🔍 测试表结构")
    print("=" * 60)
    
    try:
        from app.core.database import engine
        from sqlalchemy import text
        
        tables = ['users', 'conversations', 'messages']
        
        async with engine.begin() as conn:
            for table in tables:
                print(f"\n📋 表 '{table}' 结构:")
                result = await conn.execute(text(f"DESCRIBE {table}"))
                columns = result.fetchall()
                
                for col in columns:
                    field, type_, null, key, default, extra = col
                    print(f"  - {field}: {type_} {'NULL' if null == 'YES' else 'NOT NULL'} {key} {extra}")
        
        return True
        
    except Exception as e:
        print(f"❌ 表结构测试失败: {e}")
        return False

async def test_data_relationships():
    """测试数据关系"""
    print("\n" + "=" * 60)
    print("🔗 测试数据关系")
    print("=" * 60)
    
    try:
        from app.core.database import AsyncSessionLocal
        from app.models.user import User
        from app.models.conversation import Conversation
        from app.models.message import Message
        from sqlalchemy import select
        from sqlalchemy.orm import selectinload
        
        async with AsyncSessionLocal() as session:
            # 测试用户-对话关系
            result = await session.execute(
                select(User).options(selectinload(User.conversations)).where(User.username == "test_user_mysql")
            )
            user = result.scalar_one_or_none()
            
            if user:
                print(f"👤 用户 {user.username} 有 {len(user.conversations)} 个对话")
                
                for conv in user.conversations:
                    print(f"  💬 对话: {conv.title} (类型: {conv.dialogue_type.value})")
                    
                    # 测试对话-消息关系
                    result = await session.execute(
                        select(Conversation).options(selectinload(Conversation.messages))
                        .where(Conversation.id == conv.id)
                    )
                    conversation = result.scalar_one()
                    
                    print(f"    📝 包含 {len(conversation.messages)} 条消息")
                    for msg in conversation.messages:
                        print(f"      - {msg.sender}: {msg.content[:30]}...")
            else:
                print("❌ 未找到测试用户")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据关系测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始MySQL数据库模型测试")
    print("=" * 60)
    
    results = []
    
    # 数据库模型测试
    results.append(await test_database_models())
    
    # 用户操作测试
    results.append(await test_user_operations())
    
    # 对话操作测试
    results.append(await test_conversation_operations())
    
    # 表结构测试
    results.append(await test_table_structure())
    
    # 数据关系测试
    results.append(await test_data_relationships())
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    test_names = [
        "数据库模型",
        "用户操作",
        "对话操作", 
        "表结构",
        "数据关系"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    print(f"\n🎯 总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有数据库模型测试都通过了！")
    else:
        print("⚠️  部分测试失败，请检查数据库模型配置。")

if __name__ == "__main__":
    asyncio.run(main())
