#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Neo4j 综合测试脚本
测试Neo4j Desktop连接、基本操作和项目集成
"""

import sys
import os
import traceback
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "llm_backend"))

def test_neo4j_driver_connection():
    """测试Neo4j原生驱动连接"""
    print("\n" + "=" * 60)
    print("🔌 测试Neo4j原生驱动连接")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        # Neo4j Desktop连接参数
        NEO4J_URI = "bolt://localhost:7687"  # 或 neo4j://localhost:7687
        NEO4J_USERNAME = "neo4j"
        NEO4J_PASSWORD = "12369874c"  # 用户确认的密码
        NEO4J_DATABASE = "neo4j"
        
        print(f"📡 连接URI: {NEO4J_URI}")
        print(f"👤 用户名: {NEO4J_USERNAME}")
        print(f"🗄️ 数据库: {NEO4J_DATABASE}")
        
        # 创建驱动
        driver = GraphDatabase.driver(
            NEO4J_URI, 
            auth=(NEO4J_USERNAME, NEO4J_PASSWORD)
        )
        
        # 测试连接
        with driver.session(database=NEO4J_DATABASE) as session:
            # 验证连接
            result = session.run("RETURN 'Hello Neo4j!' as message")
            record = result.single()
            print(f"✅ 连接成功: {record['message']}")
            
            # 获取数据库信息
            result = session.run("CALL dbms.components() YIELD name, versions, edition")
            for record in result:
                print(f"📊 Neo4j版本: {record['name']} {record['versions'][0]} ({record['edition']})")
            
            # 检查数据库状态
            result = session.run("MATCH (n) RETURN count(n) as node_count")
            node_count = result.single()['node_count']
            print(f"📈 当前节点数量: {node_count}")
            
            result = session.run("MATCH ()-[r]->() RETURN count(r) as rel_count")
            rel_count = result.single()['rel_count']
            print(f"📈 当前关系数量: {rel_count}")
        
        driver.close()
        print("✅ Neo4j原生驱动测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ Neo4j原生驱动测试失败: {e}")
        traceback.print_exc()
        return False

def test_langchain_neo4j_connection():
    """测试LangChain Neo4j连接"""
    print("\n" + "=" * 60)
    print("🔗 测试LangChain Neo4j连接")
    print("=" * 60)
    
    try:
        from langchain_neo4j import Neo4jGraph
        
        # 使用项目配置
        neo4j_graph = Neo4jGraph(
            url="bolt://localhost:7687",
            username="neo4j",
            password="12369874c",  # 用户确认的密码
            database="neo4j"
        )
        
        # 测试查询
        result = neo4j_graph.query("RETURN 'LangChain Neo4j连接成功!' as message")
        print(f"✅ LangChain连接成功: {result}")
        
        # 获取schema信息
        schema = neo4j_graph.get_schema
        print(f"📋 数据库Schema: {schema}")
        
        print("✅ LangChain Neo4j测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ LangChain Neo4j测试失败: {e}")
        traceback.print_exc()
        return False

def test_project_neo4j_integration():
    """测试项目Neo4j集成"""
    print("\n" + "=" * 60)
    print("🏗️ 测试项目Neo4j集成")
    print("=" * 60)
    
    try:
        # 测试项目的Neo4j连接模块
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        print("📦 导入项目Neo4j连接模块成功")
        
        # 创建Neo4j图实例
        neo4j_graph = get_neo4j_graph()
        print("✅ 项目Neo4j图实例创建成功")
        
        # 测试基本查询
        result = neo4j_graph.query("RETURN datetime() as current_time")
        print(f"🕐 当前时间查询: {result}")
        
        print("✅ 项目Neo4j集成测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 项目Neo4j集成测试失败: {e}")
        traceback.print_exc()
        return False

def test_basic_cypher_operations():
    """测试基本Cypher操作"""
    print("\n" + "=" * 60)
    print("⚙️ 测试基本Cypher操作")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            "bolt://localhost:7687", 
            auth=("neo4j", "12369874c")
        )
        
        with driver.session(database="neo4j") as session:
            # 清理测试数据
            session.run("MATCH (n:TestNode) DETACH DELETE n")
            
            # 创建测试节点
            session.run("""
                CREATE (n:TestNode {
                    name: 'Neo4j测试节点',
                    created_at: datetime(),
                    test_id: 'test_' + toString(rand())
                })
            """)
            print("✅ 创建测试节点成功")
            
            # 查询测试节点
            result = session.run("MATCH (n:TestNode) RETURN n.name as name, n.created_at as created")
            for record in result:
                print(f"📝 测试节点: {record['name']}, 创建时间: {record['created']}")
            
            # 创建关系测试
            session.run("""
                MATCH (n:TestNode)
                CREATE (n)-[:TESTED_BY]->(:TestResult {
                    status: 'success',
                    timestamp: datetime()
                })
            """)
            print("✅ 创建测试关系成功")
            
            # 查询关系
            result = session.run("""
                MATCH (n:TestNode)-[r:TESTED_BY]->(result:TestResult)
                RETURN n.name as node_name, result.status as status
            """)
            for record in result:
                print(f"🔗 关系查询: {record['node_name']} -> {record['status']}")
            
            # 清理测试数据
            session.run("MATCH (n:TestNode) DETACH DELETE n")
            session.run("MATCH (n:TestResult) DELETE n")
            print("🧹 清理测试数据完成")
        
        driver.close()
        print("✅ 基本Cypher操作测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 基本Cypher操作测试失败: {e}")
        traceback.print_exc()
        return False

def test_neo4j_configuration():
    """测试Neo4j配置"""
    print("\n" + "=" * 60)
    print("⚙️ 测试Neo4j配置")
    print("=" * 60)
    
    try:
        # 检查配置文件
        config_file = Path("llm_backend/.env")
        if config_file.exists():
            print(f"✅ 配置文件存在: {config_file}")
            
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if "NEO4J_URL" in content:
                print("✅ 包含Neo4j URL配置")
            if "NEO4J_USERNAME" in content:
                print("✅ 包含Neo4j用户名配置")
            if "NEO4J_PASSWORD" in content:
                print("✅ 包含Neo4j密码配置")
            if "NEO4J_DATABASE" in content:
                print("✅ 包含Neo4j数据库配置")
        else:
            print(f"❌ 配置文件不存在: {config_file}")
            return False
        
        # 测试配置加载
        from app.core.config import settings
        print(f"📡 配置的Neo4j URL: {settings.NEO4J_URL}")
        print(f"👤 配置的用户名: {settings.NEO4J_USERNAME}")
        print(f"🗄️ 配置的数据库: {settings.NEO4J_DATABASE}")
        
        print("✅ Neo4j配置测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ Neo4j配置测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 Neo4j Desktop 综合测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now()}")
    print("=" * 80)
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("Neo4j配置测试", test_neo4j_configuration),
        ("Neo4j原生驱动连接", test_neo4j_driver_connection),
        ("LangChain Neo4j连接", test_langchain_neo4j_connection),
        ("项目Neo4j集成", test_project_neo4j_integration),
        ("基本Cypher操作", test_basic_cypher_operations),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有Neo4j测试都通过了！")
        print("\n💡 下一步建议:")
        print("  1. 可以开始导入测试数据")
        print("  2. 测试知识图谱查询功能")
        print("  3. 集成到LangGraph工作流中")
    else:
        print("⚠️ 部分测试失败，请检查配置和连接")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
