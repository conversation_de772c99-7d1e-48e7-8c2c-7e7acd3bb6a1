#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Neo4j数据导入测试脚本
测试业务数据导入和验证
"""

import sys
import os
import traceback
from datetime import datetime
from pathlib import Path
import pandas as pd

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "llm_backend"))

def test_data_files_availability():
    """测试数据文件可用性"""
    print("\n" + "=" * 60)
    print("📁 测试数据文件可用性")
    print("=" * 60)
    
    try:
        # 检查原始数据文件
        data_dir = Path("llm_backend/app/graphrag/origin_data/exported_data")
        expected_files = [
            'categories.csv', 'customers.csv', 'employees.csv', 
            'order_details.csv', 'orders.csv', 'products.csv',
            'reviews.csv', 'shippers.csv', 'suppliers.csv',
            'processed_reviews.csv', 'sales_report.csv'
        ]
        
        missing_files = []
        available_files = []
        
        for file_name in expected_files:
            file_path = data_dir / file_name
            if file_path.exists():
                available_files.append(file_name)
                # 检查文件大小
                size = file_path.stat().st_size
                print(f"✅ {file_name} - {size:,} bytes")
            else:
                missing_files.append(file_name)
                print(f"❌ {file_name} - 文件不存在")
        
        print(f"\n📊 数据文件统计:")
        print(f"  ✅ 可用文件: {len(available_files)}")
        print(f"  ❌ 缺失文件: {len(missing_files)}")
        
        return len(missing_files) == 0
        
    except Exception as e:
        print(f"❌ 数据文件检查失败: {e}")
        return False

def test_neo4j_import_files():
    """测试Neo4j导入文件"""
    print("\n" + "=" * 60)
    print("📦 测试Neo4j导入文件")
    print("=" * 60)
    
    try:
        # 检查Neo4j Admin导入文件
        import_dir = Path("llm_backend/app/graphrag/origin_data/data/neo4j_admin")
        
        if not import_dir.exists():
            print(f"❌ Neo4j导入目录不存在: {import_dir}")
            return False
        
        # 检查节点文件
        node_files = [
            'product_nodes.csv', 'category_nodes.csv', 'supplier_nodes.csv',
            'customer_nodes.csv', 'employee_nodes.csv', 'shipper_nodes.csv',
            'order_nodes.csv', 'review_nodes.csv'
        ]
        
        # 检查边文件
        edge_files = [
            'product_category_edges.csv', 'product_supplier_edges.csv',
            'customer_order_edges.csv', 'employee_order_edges.csv',
            'order_shipper_edges.csv', 'order_product_edges.csv',
            'employee_reports_to_edges.csv', 'customer_review_edges.csv',
            'review_product_edges.csv'
        ]
        
        all_files = node_files + edge_files
        available_count = 0
        
        print("📊 节点文件:")
        for file_name in node_files:
            file_path = import_dir / file_name
            if file_path.exists():
                size = file_path.stat().st_size
                print(f"  ✅ {file_name} - {size:,} bytes")
                available_count += 1
            else:
                print(f"  ❌ {file_name} - 缺失")
        
        print("\n🔗 关系文件:")
        for file_name in edge_files:
            file_path = import_dir / file_name
            if file_path.exists():
                size = file_path.stat().st_size
                print(f"  ✅ {file_name} - {size:,} bytes")
                available_count += 1
            else:
                print(f"  ❌ {file_name} - 缺失")
        
        # 检查导入命令文件
        import_cmd = import_dir / "import_command.bat"
        if import_cmd.exists():
            print(f"\n✅ 导入命令文件存在: {import_cmd}")
            available_count += 1
        else:
            print(f"\n❌ 导入命令文件缺失: {import_cmd}")
        
        print(f"\n📈 导入文件统计: {available_count}/{len(all_files)+1} 可用")
        
        return available_count >= len(all_files)
        
    except Exception as e:
        print(f"❌ Neo4j导入文件检查失败: {e}")
        return False

def test_cypher_data_import():
    """测试通过Cypher导入数据"""
    print("\n" + "=" * 60)
    print("🔄 测试通过Cypher导入数据")
    print("=" * 60)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        print("✅ Neo4j连接成功")
        
        # 清理现有数据
        neo4j_graph.query("MATCH (n) DETACH DELETE n")
        print("🧹 清理现有数据完成")
        
        # 导入示例产品数据
        products_data = [
            {"id": "P001", "name": "测试产品1", "price": 99.99, "category": "电子产品"},
            {"id": "P002", "name": "测试产品2", "price": 149.99, "category": "电子产品"},
            {"id": "P003", "name": "测试产品3", "price": 79.99, "category": "家居用品"}
        ]
        
        # 创建产品节点
        for product in products_data:
            result = neo4j_graph.query("""
                CREATE (p:Product {
                    id: $id,
                    name: $name,
                    price: $price,
                    category: $category,
                    created_at: datetime()
                })
                RETURN p.name as name
            """, product)
            print(f"✅ 创建产品: {result[0]['name']}")
        
        # 创建类别节点
        categories = ["电子产品", "家居用品"]
        for category in categories:
            result = neo4j_graph.query("""
                CREATE (c:Category {
                    name: $name,
                    created_at: datetime()
                })
                RETURN c.name as name
            """, {"name": category})
            print(f"✅ 创建类别: {result[0]['name']}")
        
        # 创建关系
        neo4j_graph.query("""
            MATCH (p:Product), (c:Category)
            WHERE p.category = c.name
            CREATE (p)-[:BELONGS_TO]->(c)
        """)
        print("✅ 创建产品-类别关系")
        
        # 验证导入结果
        result = neo4j_graph.query("MATCH (n) RETURN labels(n) as labels, count(n) as count")
        print("\n📊 导入结果统计:")
        for record in result:
            print(f"  {record['labels'][0]}: {record['count']} 个节点")
        
        result = neo4j_graph.query("MATCH ()-[r]->() RETURN type(r) as type, count(r) as count")
        print("\n🔗 关系统计:")
        for record in result:
            print(f"  {record['type']}: {record['count']} 个关系")
        
        print("✅ Cypher数据导入测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ Cypher数据导入测试失败: {e}")
        traceback.print_exc()
        return False

def test_data_query_functionality():
    """测试数据查询功能"""
    print("\n" + "=" * 60)
    print("🔍 测试数据查询功能")
    print("=" * 60)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        
        # 测试基本查询
        result = neo4j_graph.query("""
            MATCH (p:Product)-[:BELONGS_TO]->(c:Category)
            RETURN p.name as product, c.name as category, p.price as price
            ORDER BY p.price DESC
        """)
        
        if result:
            print("✅ 产品-类别查询成功:")
            for record in result:
                print(f"  📦 {record['product']} ({record['category']}) - ¥{record['price']}")
        else:
            print("⚠️ 查询结果为空")
        
        # 测试聚合查询
        result = neo4j_graph.query("""
            MATCH (c:Category)<-[:BELONGS_TO]-(p:Product)
            RETURN c.name as category, 
                   count(p) as product_count,
                   avg(p.price) as avg_price,
                   max(p.price) as max_price,
                   min(p.price) as min_price
        """)
        
        if result:
            print("\n✅ 聚合查询成功:")
            for record in result:
                print(f"  📊 {record['category']}: {record['product_count']}个产品, "
                      f"平均价格¥{record['avg_price']:.2f}")
        
        # 测试路径查询
        result = neo4j_graph.query("""
            MATCH path = (p:Product)-[:BELONGS_TO]->(c:Category)
            RETURN length(path) as path_length, count(*) as count
        """)
        
        if result:
            print(f"\n✅ 路径查询成功: 找到 {result[0]['count']} 条路径")
        
        print("✅ 数据查询功能测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 数据查询功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_schema_validation():
    """测试Schema验证"""
    print("\n" + "=" * 60)
    print("📋 测试Schema验证")
    print("=" * 60)
    
    try:
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        neo4j_graph = get_neo4j_graph()
        
        # 获取Schema信息
        schema = neo4j_graph.get_schema
        print("✅ Schema获取成功")
        print(f"📋 Schema内容:\n{schema}")
        
        # 验证节点标签
        result = neo4j_graph.query("CALL db.labels()")
        labels = [record[0] for record in result]
        print(f"\n🏷️ 节点标签: {labels}")
        
        # 验证关系类型
        result = neo4j_graph.query("CALL db.relationshipTypes()")
        rel_types = [record[0] for record in result]
        print(f"🔗 关系类型: {rel_types}")
        
        # 验证属性键
        result = neo4j_graph.query("CALL db.propertyKeys()")
        prop_keys = [record[0] for record in result]
        print(f"🔑 属性键: {prop_keys}")
        
        print("✅ Schema验证测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ Schema验证测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 Neo4j数据导入测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now()}")
    print("=" * 80)
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("数据文件可用性", test_data_files_availability),
        ("Neo4j导入文件", test_neo4j_import_files),
        ("Cypher数据导入", test_cypher_data_import),
        ("数据查询功能", test_data_query_functionality),
        ("Schema验证", test_schema_validation),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 数据导入测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed >= 4:
        print("🎉 数据导入环境就绪！")
        print("\n💡 下一步建议:")
        print("  1. 运行完整的业务数据导入")
        print("  2. 测试复杂的图查询")
        print("  3. 集成到LangGraph工作流")
    else:
        print("⚠️ 数据导入环境需要进一步配置")
    
    return passed >= 4

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
