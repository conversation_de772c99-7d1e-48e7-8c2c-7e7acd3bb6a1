#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
APOC插件验证和配置测试脚本
"""

import sys
import traceback
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "llm_backend"))

def test_apoc_installation():
    """测试APOC插件安装情况"""
    print("\n" + "=" * 60)
    print("🔌 测试APOC插件安装情况")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            "bolt://localhost:7687", 
            auth=("neo4j", "12369874c")
        )
        
        with driver.session(database="neo4j") as session:
            # 测试基本APOC功能
            try:
                result = session.run("CALL apoc.version()")
                record = result.single()
                if record:
                    print(f"✅ APOC版本: {record[0]}")
                else:
                    print("⚠️ APOC版本查询无结果")
            except Exception as e:
                print(f"❌ APOC版本查询失败: {e}")
            
            # 测试APOC帮助功能
            try:
                result = session.run("CALL apoc.help('meta') YIELD name RETURN count(name) as count")
                record = result.single()
                print(f"✅ APOC meta相关功能数量: {record['count']}")
            except Exception as e:
                print(f"❌ APOC帮助查询失败: {e}")
            
            # 测试具体的apoc.meta.data
            try:
                result = session.run("CALL apoc.meta.data()")
                records = list(result)
                print(f"✅ apoc.meta.data 执行成功，返回 {len(records)} 条记录")
            except Exception as e:
                print(f"❌ apoc.meta.data 执行失败: {e}")
                print("💡 这是预期的错误，需要配置安全设置")
            
            # 测试其他APOC功能
            try:
                result = session.run("CALL apoc.date.format(timestamp(), 'yyyy-MM-dd')")
                record = result.single()
                print(f"✅ APOC日期功能正常: {record[0]}")
            except Exception as e:
                print(f"❌ APOC日期功能失败: {e}")
        
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ APOC测试失败: {e}")
        traceback.print_exc()
        return False

def test_apoc_workaround():
    """测试APOC绕过方案"""
    print("\n" + "=" * 60)
    print("🔧 测试APOC绕过方案")
    print("=" * 60)
    
    try:
        from langchain_neo4j import Neo4jGraph
        
        # 尝试使用enhanced_schema=False来绕过APOC依赖
        neo4j_graph = Neo4jGraph(
            url="bolt://localhost:7687",
            username="neo4j",
            password="12369874c",
            database="neo4j",
            enhanced_schema=False  # 关键参数：禁用增强schema
        )
        
        print("✅ LangChain Neo4j连接成功（禁用增强schema）")
        
        # 测试基本查询
        result = neo4j_graph.query("RETURN 'APOC绕过方案测试成功!' as message")
        print(f"📝 查询结果: {result}")
        
        # 测试简单的schema获取
        try:
            schema = neo4j_graph.get_schema
            print(f"📋 简化Schema: {schema}")
        except Exception as e:
            print(f"⚠️ Schema获取失败: {e}")
        
        print("✅ APOC绕过方案测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ APOC绕过方案测试失败: {e}")
        traceback.print_exc()
        return False

def test_manual_schema():
    """测试手动Schema创建"""
    print("\n" + "=" * 60)
    print("📋 测试手动Schema创建")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            "bolt://localhost:7687", 
            auth=("neo4j", "12369874c")
        )
        
        with driver.session(database="neo4j") as session:
            # 获取节点标签
            result = session.run("CALL db.labels()")
            labels = [record[0] for record in result]
            print(f"📊 节点标签: {labels}")
            
            # 获取关系类型
            result = session.run("CALL db.relationshipTypes()")
            rel_types = [record[0] for record in result]
            print(f"🔗 关系类型: {rel_types}")
            
            # 获取属性键
            result = session.run("CALL db.propertyKeys()")
            prop_keys = [record[0] for record in result]
            print(f"🔑 属性键: {prop_keys}")
            
            # 创建简单的schema信息
            schema_info = {
                "node_labels": labels,
                "relationship_types": rel_types,
                "property_keys": prop_keys
            }
            
            print(f"✅ 手动Schema创建成功: {schema_info}")
        
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ 手动Schema创建失败: {e}")
        traceback.print_exc()
        return False

def create_apoc_config_guide():
    """创建APOC配置指南"""
    print("\n" + "=" * 60)
    print("📖 APOC配置指南")
    print("=" * 60)
    
    config_guide = """
🔧 Neo4j APOC配置解决方案：

方法1：在Neo4j Desktop中配置
1. 停止Neo4j实例
2. 点击实例的"Settings"或"设置"
3. 在配置文件中添加以下行：
   dbms.security.procedures.unrestricted=apoc.*
4. 保存并重启实例

方法2：编辑neo4j.conf文件
1. 找到Neo4j安装目录下的conf/neo4j.conf文件
2. 添加或修改以下配置：
   dbms.security.procedures.unrestricted=apoc.*
   dbms.security.procedures.allowlist=apoc.*
3. 重启Neo4j服务

方法3：使用项目绕过方案（推荐）
- 在代码中使用 enhanced_schema=False 参数
- 这样可以避免依赖APOC的meta功能

当前推荐使用方法3，因为它不需要修改Neo4j配置。
    """
    
    print(config_guide)
    
    # 保存配置指南到文件
    guide_file = Path("apoc_config_guide.txt")
    with open(guide_file, 'w', encoding='utf-8') as f:
        f.write(config_guide)
    
    print(f"✅ 配置指南已保存到: {guide_file}")
    return True

def main():
    """主测试函数"""
    print("🚀 APOC插件验证和配置测试")
    print("=" * 80)
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("APOC插件安装情况", test_apoc_installation),
        ("APOC绕过方案", test_apoc_workaround),
        ("手动Schema创建", test_manual_schema),
        ("APOC配置指南", create_apoc_config_guide),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 APOC测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed >= 2:  # 至少绕过方案成功
        print("🎉 APOC问题已有解决方案！")
        print("\n💡 建议:")
        print("  1. 使用enhanced_schema=False绕过APOC依赖")
        print("  2. 或者按照配置指南配置Neo4j安全设置")
        print("  3. 继续进行数据导入和测试")
    else:
        print("⚠️ 需要进一步配置APOC")
    
    return passed >= 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
