#!/usr/bin/env python3
"""
Redis综合测试脚本
测试项目中所有Redis相关功能
"""

import os
import sys
import asyncio
import json
import time
from pathlib import Path
from datetime import datetime

# 添加项目路径到sys.path
project_root = Path(__file__).parent
llm_backend_path = project_root / "llm_backend"
sys.path.insert(0, str(llm_backend_path))

# 设置环境变量文件路径
env_file = llm_backend_path / ".env"
os.environ.setdefault("ENV_FILE", str(env_file))

import redis
from dotenv import load_dotenv
load_dotenv(env_file)

def test_basic_redis_connection():
    """测试基本Redis连接"""
    print("=" * 60)
    print("🔴 测试基本Redis连接")
    print("=" * 60)
    
    try:
        from app.core.config import settings
        
        # 显示配置信息
        print(f"📊 Redis配置:")
        print(f"  - 主机: {settings.REDIS_HOST}")
        print(f"  - 端口: {settings.REDIS_PORT}")
        print(f"  - 数据库: {settings.REDIS_DB}")
        print(f"  - 密码: {'设置' if settings.REDIS_PASSWORD else '未设置'}")
        print(f"  - URL: {settings.REDIS_URL.replace(settings.REDIS_PASSWORD or '', '***')}")
        
        # 创建Redis连接
        r = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB,
            password=settings.REDIS_PASSWORD if settings.REDIS_PASSWORD else None,
            socket_connect_timeout=5,
            decode_responses=True
        )
        
        # 测试连接
        r.ping()
        print("✅ Redis连接成功!")
        
        # 获取Redis信息
        info = r.info()
        print(f"📊 Redis版本: {info['redis_version']}")
        print(f"📊 运行模式: {info['redis_mode']}")
        print(f"📊 已用内存: {info['used_memory_human']}")
        print(f"📊 连接数: {info['connected_clients']}")
        
        # 测试基本操作
        print("\n🔧 测试基本操作:")
        
        # SET/GET测试
        test_key = "test:basic:connection"
        test_value = f"测试时间: {datetime.now()}"
        r.set(test_key, test_value, ex=60)
        retrieved_value = r.get(test_key)
        print(f"✅ SET/GET测试: {retrieved_value}")
        
        # 列表操作测试
        list_key = "test:list"
        r.delete(list_key)  # 清除可能存在的旧数据
        r.lpush(list_key, "item1", "item2", "item3")
        list_items = r.lrange(list_key, 0, -1)
        print(f"✅ 列表操作测试: {list_items}")
        
        # 哈希操作测试
        hash_key = "test:hash"
        r.delete(hash_key)
        r.hset(hash_key, mapping={
            "name": "Redis测试",
            "type": "缓存",
            "timestamp": str(datetime.now())
        })
        hash_data = r.hgetall(hash_key)
        print(f"✅ 哈希操作测试: {hash_data}")
        
        # 清理测试数据
        r.delete(test_key, list_key, hash_key)
        print("✅ 测试数据清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_redis_performance():
    """测试Redis性能"""
    print("\n" + "=" * 60)
    print("⚡ 测试Redis性能")
    print("=" * 60)
    
    try:
        from app.core.config import settings
        
        r = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB,
            password=settings.REDIS_PASSWORD if settings.REDIS_PASSWORD else None,
            decode_responses=True
        )
        
        # 批量写入测试
        print("📝 批量写入测试 (1000条记录)...")
        start_time = time.time()
        
        pipe = r.pipeline()
        for i in range(1000):
            pipe.set(f"perf:test:{i}", f"value_{i}", ex=300)
        pipe.execute()
        
        write_time = time.time() - start_time
        print(f"✅ 批量写入完成，耗时: {write_time:.3f}秒")
        
        # 批量读取测试
        print("📖 批量读取测试 (1000条记录)...")
        start_time = time.time()
        
        pipe = r.pipeline()
        for i in range(1000):
            pipe.get(f"perf:test:{i}")
        results = pipe.execute()
        
        read_time = time.time() - start_time
        print(f"✅ 批量读取完成，耗时: {read_time:.3f}秒")
        print(f"📊 读取成功率: {len([r for r in results if r is not None])/1000*100:.1f}%")
        
        # 清理性能测试数据
        print("🧹 清理性能测试数据...")
        keys_to_delete = [f"perf:test:{i}" for i in range(1000)]
        if keys_to_delete:
            r.delete(*keys_to_delete)
        print("✅ 性能测试数据清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis性能测试失败: {e}")
        return False

async def test_semantic_cache():
    """测试语义缓存功能"""
    print("\n" + "=" * 60)
    print("🧠 测试语义缓存功能")
    print("=" * 60)
    
    try:
        from app.services.redis_semantic_cache import RedisSemanticCache
        from app.core.config import settings
        
        print(f"📊 语义缓存配置:")
        print(f"  - Embedding模型: {settings.OLLAMA_EMBEDDING_MODEL}")
        print(f"  - 缓存阈值: {settings.REDIS_CACHE_THRESHOLD}")
        print(f"  - 缓存过期时间: {settings.REDIS_CACHE_EXPIRE}秒")
        
        # 创建缓存实例
        cache = RedisSemanticCache(prefix="test", user_id=999)
        print("✅ 语义缓存实例创建成功")
        
        # 测试消息
        test_messages = [
            {"role": "user", "content": "你好，请介绍一下你自己"}
        ]
        
        print(f"📝 测试消息: {test_messages[0]['content']}")
        
        # 1. 测试 embedding 生成
        print("\n🔍 测试 Ollama embedding 生成...")
        start_time = time.time()
        embedding = await cache._get_embedding(test_messages[0]["content"])
        embedding_time = time.time() - start_time
        
        print(f"✅ Embedding 生成成功!")
        print(f"📊 向量维度: {len(embedding)}")
        print(f"📊 生成耗时: {embedding_time:.3f}秒")
        print(f"📊 向量前5个值: {embedding[:5]}")
        
        # 2. 测试缓存存储
        print("\n💾 测试缓存存储...")
        test_response = "你好！我是一个AI助手，很高兴为您服务。我可以帮助您解答问题、提供信息和协助完成各种任务。"
        await cache.update(test_messages, test_response)
        print("✅ 缓存存储成功!")
        
        # 3. 测试缓存查找
        print("\n🔍 测试缓存查找...")
        start_time = time.time()
        cached_response = await cache.lookup(test_messages)
        lookup_time = time.time() - start_time
        
        if cached_response:
            print("✅ 缓存查找成功!")
            print(f"📄 缓存内容: {cached_response[:50]}...")
            print(f"⚡ 查找耗时: {lookup_time:.3f}秒")
        else:
            print("❌ 缓存查找失败")
            
        # 4. 测试相似问题的缓存命中
        print("\n🔍 测试相似问题缓存命中...")
        similar_messages = [
            {"role": "user", "content": "你好，能介绍下你自己吗"}  # 相似问题
        ]
        
        start_time = time.time()
        similar_cached = await cache.lookup(similar_messages)
        similar_lookup_time = time.time() - start_time
        
        if similar_cached:
            print("✅ 相似问题缓存命中!")
            print(f"📄 缓存内容: {similar_cached[:50]}...")
            print(f"⚡ 查找耗时: {similar_lookup_time:.3f}秒")
        else:
            print("❌ 相似问题未命中缓存")
            
        # 5. 测试不同问题（应该不命中）
        print("\n🔍 测试不同问题（应该不命中缓存）...")
        different_messages = [
            {"role": "user", "content": "今天天气怎么样？"}
        ]
        
        different_cached = await cache.lookup(different_messages)
        if different_cached:
            print("⚠️  不同问题意外命中缓存")
        else:
            print("✅ 不同问题正确未命中缓存")
            
        # 6. 测试缓存统计
        print("\n📊 测试缓存统计...")
        try:
            # 获取缓存键数量
            from app.core.config import settings
            r = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD if settings.REDIS_PASSWORD else None
            )
            
            cache_keys = r.keys("test:999:*")
            print(f"📊 当前缓存键数量: {len(cache_keys)}")
            
            # 显示缓存键类型
            key_types = {}
            for key in cache_keys:
                key_str = key.decode('utf-8') if isinstance(key, bytes) else key
                if ':vec:' in key_str:
                    key_types['向量'] = key_types.get('向量', 0) + 1
                elif ':resp:' in key_str:
                    key_types['响应'] = key_types.get('响应', 0) + 1
                elif ':meta:' in key_str:
                    key_types['元数据'] = key_types.get('元数据', 0) + 1
            
            print(f"📊 缓存键类型分布: {key_types}")
            
        except Exception as e:
            print(f"⚠️  缓存统计获取失败: {e}")
        
        print("\n🎉 语义缓存功能测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 语义缓存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_redis_data_types():
    """测试Redis数据类型操作"""
    print("\n" + "=" * 60)
    print("🗃️  测试Redis数据类型操作")
    print("=" * 60)
    
    try:
        from app.core.config import settings
        
        r = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB,
            password=settings.REDIS_PASSWORD if settings.REDIS_PASSWORD else None,
            decode_responses=True
        )
        
        # 字符串操作
        print("📝 字符串操作测试:")
        r.set("test:string", "Hello Redis", ex=60)
        r.append("test:string", " from Python!")
        string_val = r.get("test:string")
        print(f"  ✅ 字符串值: {string_val}")
        
        # 列表操作
        print("📋 列表操作测试:")
        list_key = "test:list:advanced"
        r.delete(list_key)
        r.rpush(list_key, "first", "second", "third")
        r.lpush(list_key, "zero")
        list_len = r.llen(list_key)
        list_range = r.lrange(list_key, 0, -1)
        print(f"  ✅ 列表长度: {list_len}, 内容: {list_range}")
        
        # 集合操作
        print("🔢 集合操作测试:")
        set_key = "test:set"
        r.delete(set_key)
        r.sadd(set_key, "apple", "banana", "cherry", "apple")  # apple重复
        set_members = r.smembers(set_key)
        set_size = r.scard(set_key)
        print(f"  ✅ 集合大小: {set_size}, 成员: {set_members}")
        
        # 有序集合操作
        print("📊 有序集合操作测试:")
        zset_key = "test:zset"
        r.delete(zset_key)
        r.zadd(zset_key, {"user1": 100, "user2": 200, "user3": 150})
        top_users = r.zrevrange(zset_key, 0, 2, withscores=True)
        print(f"  ✅ 排行榜前3: {top_users}")
        
        # 哈希操作
        print("🗂️  哈希操作测试:")
        hash_key = "test:hash:user"
        r.delete(hash_key)
        r.hmset(hash_key, {
            "name": "张三",
            "age": "25",
            "city": "北京",
            "score": "95"
        })
        user_info = r.hgetall(hash_key)
        print(f"  ✅ 用户信息: {user_info}")
        
        # 过期时间测试
        print("⏰ 过期时间测试:")
        expire_key = "test:expire"
        r.set(expire_key, "这条数据5秒后过期", ex=5)
        ttl = r.ttl(expire_key)
        print(f"  ✅ 剩余生存时间: {ttl}秒")
        
        # 清理测试数据
        test_keys = [
            "test:string", list_key, set_key, zset_key, hash_key, expire_key
        ]
        r.delete(*test_keys)
        print("✅ 数据类型测试数据清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis数据类型测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始Redis综合测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    results = []
    
    # 基本连接测试
    results.append(("基本Redis连接", test_basic_redis_connection()))
    
    # 性能测试
    results.append(("Redis性能", test_redis_performance()))
    
    # 数据类型测试
    results.append(("Redis数据类型", test_redis_data_types()))
    
    # 语义缓存测试
    results.append(("语义缓存功能", await test_semantic_cache()))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 Redis测试结果总结")
    print("=" * 60)
    
    for i, (name, result) in enumerate(results, 1):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i}. {name}: {status}")
    
    success_count = sum(results[i][1] for i in range(len(results)))
    total_count = len(results)
    print(f"\n🎯 总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有Redis测试都通过了！")
    else:
        print("⚠️  部分Redis测试失败，请检查配置和服务状态。")

if __name__ == "__main__":
    asyncio.run(main())
