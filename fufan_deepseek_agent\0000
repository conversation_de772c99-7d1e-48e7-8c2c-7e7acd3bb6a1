pip install "transformers>=4.41.0,<5.0.0"

启动服务
ollama serve

确认服务运行在
localhost:11434

# 方法1：使用 ollama 命令
ollama pull bge-m3

# 方法2：如果命令行不可用，可以使用我们的 Python 脚本
python test_ollama.py


# 在当前conda环境中
pip install graphrag==2.1.0
# 或者安装缺失的具体依赖
pip install fnllm graspologic datashaper scikit-learn


mysql密码
C:\Users\<USER>\Desktop\test\004-asssgen\fufan_deepseek_agent\llm_backend\app\graphrag\origin_data\create_sql_data.py
# 1. 首先确保MySQL服务运行正常
# 2. 创建graphrag数据库（如果还没有）
python create_graphrag_db.py

# 3. 生成GraphRAG测试数据（可选，但建议执行）
cd llm_backend/app/graphrag/origin_data
python create_sql_data.py

# 4. 启动主应用
cd llm_backend
python main.py

neo4j 导入数据
APOC插件
密码
import_business_data.py
create_neo4j_import.py
