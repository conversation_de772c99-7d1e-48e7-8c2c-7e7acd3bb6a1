#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
APOC 2025.07.1 版本验证脚本
"""

import sys
import traceback
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "llm_backend"))

def test_apoc_2025_07_1():
    """测试APOC 2025.07.1版本"""
    print("\n" + "=" * 60)
    print("🔍 测试APOC 2025.07.1版本")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            "bolt://localhost:7687", 
            auth=("neo4j", "12369874c")
        )
        
        with driver.session(database="neo4j") as session:
            # 检查APOC版本
            try:
                result = session.run("CALL apoc.version()")
                record = result.single()
                if record:
                    version = record[0]
                    print(f"✅ APOC版本: {version}")
                    
                    if "2025.07.1" in version:
                        print("🎉 APOC版本完全匹配Neo4j 2025.07.1!")
                        return True
                    else:
                        print(f"⚠️ APOC版本不匹配，期望: 2025.07.1，实际: {version}")
                        return False
                else:
                    print("❌ 无法获取APOC版本")
                    return False
            except Exception as e:
                print(f"❌ APOC版本查询失败: {e}")
                return False
        
        driver.close()
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_apoc_meta_data():
    """测试apoc.meta.data功能"""
    print("\n" + "=" * 60)
    print("🔧 测试apoc.meta.data功能")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            "bolt://localhost:7687", 
            auth=("neo4j", "12369874c")
        )
        
        with driver.session(database="neo4j") as session:
            try:
                result = session.run("CALL apoc.meta.data()")
                records = list(result)
                print(f"✅ apoc.meta.data 执行成功，返回 {len(records)} 条记录")
                
                if len(records) > 0:
                    print("📊 Meta数据示例:")
                    for i, record in enumerate(records[:3]):  # 显示前3条
                        print(f"  {i+1}. {dict(record)}")
                    if len(records) > 3:
                        print(f"  ... 还有 {len(records) - 3} 条记录")
                
                return True
            except Exception as e:
                print(f"❌ apoc.meta.data 执行失败: {e}")
                return False
        
        driver.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_langchain_with_apoc():
    """测试LangChain与APOC的集成"""
    print("\n" + "=" * 60)
    print("🔗 测试LangChain与APOC集成")
    print("=" * 60)
    
    try:
        from langchain_neo4j import Neo4jGraph
        
        # 使用原始LangChain Neo4j连接（不禁用enhanced_schema）
        neo4j_graph = Neo4jGraph(
            url="bolt://localhost:7687",
            username="neo4j",
            password="12369874c",
            database="neo4j"
        )
        
        print("✅ LangChain Neo4j连接成功（APOC正常工作）")
        
        # 测试查询
        result = neo4j_graph.query("RETURN 'LangChain + APOC 2025.07.1 成功!' as message")
        print(f"📝 查询结果: {result}")
        
        # 测试schema获取
        try:
            schema = neo4j_graph.get_schema
            print(f"📋 Schema获取成功，长度: {len(schema)} 字符")
            print(f"📋 Schema预览: {schema[:200]}...")
        except Exception as e:
            print(f"⚠️ Schema获取失败: {e}")
        
        print("✅ LangChain与APOC集成测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ LangChain与APOC集成测试失败: {e}")
        traceback.print_exc()
        return False

def test_project_integration_with_apoc():
    """测试项目与APOC的集成"""
    print("\n" + "=" * 60)
    print("🏗️ 测试项目与APOC集成")
    print("=" * 60)
    
    try:
        # 临时恢复原始LangChain连接
        from langchain_neo4j import Neo4jGraph
        from app.core.config import settings
        
        # 直接使用LangChain Neo4j（不使用自定义类）
        neo4j_graph = Neo4jGraph(
            url=settings.NEO4J_URL,
            username=settings.NEO4J_USERNAME,
            password=settings.NEO4J_PASSWORD,
            database=settings.NEO4J_DATABASE
        )
        
        print("✅ 项目LangChain Neo4j连接成功（APOC已修复）")
        
        # 测试基本查询
        result = neo4j_graph.query("RETURN '项目APOC集成成功!' as message")
        print(f"📝 查询结果: {result}")
        
        # 测试schema功能
        try:
            schema = neo4j_graph.get_schema
            print(f"📋 项目Schema获取成功")
        except Exception as e:
            print(f"⚠️ 项目Schema获取失败: {e}")
        
        print("✅ 项目与APOC集成测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 项目与APOC集成测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 APOC 2025.07.1 版本验证")
    print("=" * 80)
    print("🎯 验证APOC与Neo4j 2025.07.1的完美匹配")
    print("=" * 80)
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("APOC 2025.07.1版本检查", test_apoc_2025_07_1),
        ("apoc.meta.data功能", test_apoc_meta_data),
        ("LangChain与APOC集成", test_langchain_with_apoc),
        ("项目与APOC集成", test_project_integration_with_apoc),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 APOC 2025.07.1 验证结果")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 APOC 2025.07.1 完美工作！")
        print("\n💡 现在可以:")
        print("  1. 使用原始LangChain Neo4j连接")
        print("  2. 享受完整的APOC功能")
        print("  3. 开始正式的项目开发")
    elif passed >= 2:
        print("🎯 APOC基本功能正常！")
        print("💡 可以开始使用APOC功能")
    else:
        print("⚠️ APOC仍需要正确安装")
        print("💡 请确保安装APOC 2025.07.1版本")
    
    return passed >= 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
