#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
APOC插件详细诊断脚本
"""

import sys
import traceback
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "llm_backend"))

def test_neo4j_connection():
    """测试基本Neo4j连接"""
    print("\n" + "=" * 60)
    print("🔌 测试基本Neo4j连接")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            "bolt://localhost:7687", 
            auth=("neo4j", "12369874c")
        )
        
        with driver.session(database="neo4j") as session:
            result = session.run("RETURN 'Neo4j连接成功!' as message")
            record = result.single()
            print(f"✅ {record['message']}")
            
            # 检查Neo4j版本
            result = session.run("CALL dbms.components() YIELD name, versions, edition")
            for record in result:
                print(f"📊 {record['name']}: {record['versions'][0]} ({record['edition']})")
        
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ Neo4j连接失败: {e}")
        return False

def test_available_procedures():
    """检查可用的过程"""
    print("\n" + "=" * 60)
    print("📋 检查可用的过程")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            "bolt://localhost:7687", 
            auth=("neo4j", "12369874c")
        )
        
        with driver.session(database="neo4j") as session:
            # 检查所有可用过程
            result = session.run("CALL dbms.procedures() YIELD name WHERE name CONTAINS 'apoc' RETURN name LIMIT 10")
            apoc_procedures = [record['name'] for record in result]
            
            if apoc_procedures:
                print(f"✅ 找到 {len(apoc_procedures)} 个APOC过程:")
                for proc in apoc_procedures[:10]:
                    print(f"  - {proc}")
                if len(apoc_procedures) > 10:
                    print(f"  ... 还有 {len(apoc_procedures) - 10} 个过程")
            else:
                print("❌ 没有找到APOC过程")
                
                # 检查所有过程
                result = session.run("CALL dbms.procedures() YIELD name RETURN count(name) as total")
                total = result.single()['total']
                print(f"📊 总共有 {total} 个可用过程")
                
                # 显示前几个过程
                result = session.run("CALL dbms.procedures() YIELD name RETURN name LIMIT 5")
                print("📋 前5个过程:")
                for record in result:
                    print(f"  - {record['name']}")
        
        driver.close()
        return len(apoc_procedures) > 0
        
    except Exception as e:
        print(f"❌ 检查过程失败: {e}")
        return False

def test_apoc_functions():
    """检查APOC函数"""
    print("\n" + "=" * 60)
    print("🔧 检查APOC函数")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            "bolt://localhost:7687", 
            auth=("neo4j", "12369874c")
        )
        
        with driver.session(database="neo4j") as session:
            # 检查APOC函数
            result = session.run("CALL dbms.functions() YIELD name WHERE name CONTAINS 'apoc' RETURN name LIMIT 10")
            apoc_functions = [record['name'] for record in result]
            
            if apoc_functions:
                print(f"✅ 找到 {len(apoc_functions)} 个APOC函数:")
                for func in apoc_functions[:10]:
                    print(f"  - {func}")
            else:
                print("❌ 没有找到APOC函数")
        
        driver.close()
        return len(apoc_functions) > 0
        
    except Exception as e:
        print(f"❌ 检查函数失败: {e}")
        return False

def test_plugin_directory():
    """检查插件目录信息"""
    print("\n" + "=" * 60)
    print("📁 检查插件目录信息")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            "bolt://localhost:7687", 
            auth=("neo4j", "12369874c")
        )
        
        with driver.session(database="neo4j") as session:
            # 检查配置
            try:
                result = session.run("CALL dbms.listConfig() YIELD name, value WHERE name CONTAINS 'plugin' RETURN name, value")
                plugin_configs = [(record['name'], record['value']) for record in result]
                
                if plugin_configs:
                    print("✅ 插件相关配置:")
                    for name, value in plugin_configs:
                        print(f"  - {name}: {value}")
                else:
                    print("⚠️ 没有找到插件相关配置")
            except Exception as e:
                print(f"⚠️ 无法获取插件配置: {e}")
            
            # 检查安全配置
            try:
                result = session.run("CALL dbms.listConfig() YIELD name, value WHERE name CONTAINS 'security.procedures' RETURN name, value")
                security_configs = [(record['name'], record['value']) for record in result]
                
                if security_configs:
                    print("✅ 安全过程配置:")
                    for name, value in security_configs:
                        print(f"  - {name}: {value}")
                else:
                    print("⚠️ 没有找到安全过程配置")
            except Exception as e:
                print(f"⚠️ 无法获取安全配置: {e}")
        
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查插件目录失败: {e}")
        return False

def provide_apoc_troubleshooting():
    """提供APOC故障排除建议"""
    print("\n" + "=" * 60)
    print("🔧 APOC故障排除建议")
    print("=" * 60)
    
    troubleshooting = """
🚨 APOC插件故障排除步骤：

1. 确认APOC插件安装：
   - 在Neo4j Desktop中，检查Plugins标签
   - 确保APOC插件已安装并启用

2. 检查Neo4j版本兼容性：
   - APOC版本必须与Neo4j版本匹配
   - 查看APOC官方兼容性表

3. 重新安装APOC插件：
   - 在Neo4j Desktop中卸载APOC
   - 重新安装APOC插件
   - 重启Neo4j实例

4. 检查配置文件：
   - 确保neo4j.conf中有正确的配置
   - dbms.security.procedures.unrestricted=gds.*,apoc.*
   - dbms.security.procedures.allowlist=apoc.*,gds.*

5. 检查日志文件：
   - 查看Neo4j日志中的错误信息
   - 在Neo4j Desktop中点击"Open neo4j.log"

6. 临时解决方案：
   - 使用我们创建的自定义Neo4j连接类
   - 完全绕过APOC依赖
    """
    
    print(troubleshooting)
    return True

def main():
    """主诊断函数"""
    print("🚀 APOC插件详细诊断")
    print("=" * 80)
    
    # 执行诊断测试
    tests = [
        ("Neo4j基本连接", test_neo4j_connection),
        ("可用过程检查", test_available_procedures),
        ("APOC函数检查", test_apoc_functions),
        ("插件目录信息", test_plugin_directory),
        ("故障排除建议", provide_apoc_troubleshooting),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            results.append((test_name, False))
    
    # 汇总诊断结果
    print("\n" + "=" * 80)
    print("📊 诊断结果汇总")
    print("=" * 80)
    
    for test_name, result in results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{status} {test_name}")
    
    # 提供建议
    apoc_working = results[1][1] or results[2][1]  # 过程或函数有一个工作就算成功
    
    if apoc_working:
        print("\n🎉 APOC插件部分功能正常！")
        print("💡 可以尝试重新运行原始测试")
    else:
        print("\n⚠️ APOC插件未正确加载")
        print("💡 建议:")
        print("  1. 重新安装APOC插件")
        print("  2. 检查版本兼容性")
        print("  3. 使用自定义Neo4j连接类作为替代方案")
    
    return apoc_working

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
