2025-02-21 13:59:17.797 | INFO     | __main__:start_server:12 - Starting server...
2025-02-21 13:59:17.797 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-21 14:00:29.148 | INFO     | __main__:start_server:12 - Starting server...
2025-02-21 14:00:29.149 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-21 14:02:54.691 | INFO     | __main__:start_server:12 - Starting server...
2025-02-21 14:02:54.691 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-21 14:06:18.594 | INFO     | __main__:start_server:12 - Starting server...
2025-02-21 14:06:18.595 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-21 14:12:41.086 | INFO     | __main__:start_server:12 - Starting server...
2025-02-21 14:12:41.086 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-21 14:17:30.519 | INFO     | __main__:start_server:12 - Starting server...
2025-02-21 14:17:30.520 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-21 14:20:07.796 | INFO     | __main__:start_server:12 - Starting server...
2025-02-21 14:20:07.797 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-21 14:23:01.914 | INFO     | __main__:start_server:12 - Starting server...
2025-02-21 14:23:01.914 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-21 14:25:50.439 | INFO     | __main__:start_server:12 - Starting server...
2025-02-21 14:25:50.440 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-21 14:26:46.941 | INFO     | __main__:start_server:12 - Starting server...
2025-02-21 14:26:46.942 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-21 14:36:47.750 | INFO     | __main__:start_server:12 - Starting server...
2025-02-21 14:36:47.750 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-24 10:47:54.453 | INFO     | __main__:start_server:12 - Starting server...
2025-02-24 10:47:54.454 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-24 11:25:22.118 | INFO     | __main__:start_server:12 - Starting server...
2025-02-24 11:25:22.119 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-26 20:38:42.678 | INFO     | __main__:start_server:12 - Starting server...
2025-02-26 20:38:42.679 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-26 20:41:12.029 | INFO     | __main__:start_server:12 - Starting server...
2025-02-26 20:41:12.030 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-26 20:44:09.741 | INFO     | __main__:start_server:12 - Starting server...
2025-02-26 20:44:09.741 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-26 20:46:56.716 | INFO     | __main__:start_server:12 - Starting server...
2025-02-26 20:46:56.717 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-26 20:49:33.542 | INFO     | __main__:start_server:12 - Starting server...
2025-02-26 20:49:33.542 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-26 20:51:45.647 | INFO     | __main__:start_server:12 - Starting server...
2025-02-26 20:51:45.647 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-26 20:53:37.701 | INFO     | __main__:start_server:12 - Starting server...
2025-02-26 20:53:37.701 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-26 20:58:47.809 | INFO     | __main__:start_server:12 - Starting server...
2025-02-26 20:58:47.810 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-27 16:09:16.782 | INFO     | __main__:start_server:12 - Starting server...
2025-02-27 16:09:16.783 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-02-28 10:16:29.119 | INFO     | __main__:start_server:12 - Starting server...
2025-02-28 10:16:29.120 | INFO     | __main__:start_server:13 - Working directory: e:\01_木羽研发\04_Agent正课\【项目开发实战】DeepSeek 项目开发\fufan_deepseek_agent\llm_backend
2025-08-23 22:58:20.976 | INFO     | app.services.redis_semantic_cache:_auto_cleanup:114 - Cache cleanup completed for prefix test:1
2025-08-23 22:59:12.420 | INFO     | app.services.redis_semantic_cache:_auto_cleanup:114 - Cache cleanup completed for prefix test:1
2025-08-23 22:59:13.871 | INFO     | app.services.redis_semantic_cache:update:220 - Cache updated for message: 你好，请介绍一下你自己...
2025-08-23 22:59:14.522 | INFO     | app.services.redis_semantic_cache:lookup:185 - Cache hit with similarity: 1.0000
2025-08-23 22:59:15.119 | INFO     | app.services.redis_semantic_cache:lookup:185 - Cache hit with similarity: 0.9763
2025-08-24 13:21:43.250 | INFO     | app.services.redis_semantic_cache:_auto_cleanup:114 - Cache cleanup completed for prefix test:999
2025-08-24 13:21:48.408 | INFO     | app.services.redis_semantic_cache:update:220 - Cache updated for message: 你好，请介绍一下你自己...
2025-08-24 13:21:49.001 | INFO     | app.services.redis_semantic_cache:lookup:185 - Cache hit with similarity: 1.0000
2025-08-24 13:21:49.621 | INFO     | app.services.redis_semantic_cache:lookup:185 - Cache hit with similarity: 0.9763
2025-08-24 13:22:16.031 | INFO     | app.services.redis_semantic_cache:_auto_cleanup:114 - Cache cleanup completed for prefix test:1
2025-08-24 13:22:17.429 | INFO     | app.services.redis_semantic_cache:update:220 - Cache updated for message: 你好，请介绍一下你自己...
2025-08-24 13:22:18.050 | INFO     | app.services.redis_semantic_cache:lookup:185 - Cache hit with similarity: 1.0000
2025-08-24 13:22:18.665 | INFO     | app.services.redis_semantic_cache:lookup:185 - Cache hit with similarity: 0.9763
2025-08-24 13:40:24.379 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:24 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 13:44:21.338 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:24 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 13:45:14.728 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:24 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 13:59:42.598 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:24 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:00:29.481 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:24 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:03:48.711 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:24 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:06:47.722 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:06:49.813 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - Custom Neo4j graph instance created successfully
2025-08-24 14:06:52.725 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:06:54.798 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - Custom Neo4j graph instance created successfully
2025-08-24 14:22:11.703 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:22:13.792 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - Custom Neo4j graph instance created successfully
2025-08-24 14:22:17.774 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:22:19.868 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - Custom Neo4j graph instance created successfully
2025-08-24 14:37:15.545 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:37:17.631 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:43:20.055 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:43:22.160 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:43:22.640 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:43:24.735 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:43:25.047 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:43:27.137 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:44:06.560 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:44:08.664 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:44:08.676 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:44:10.794 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:44:11.198 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:44:13.315 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:44:13.623 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:44:15.822 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:44:16.071 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:44:18.167 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:44:18.490 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:44:20.774 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:44:21.515 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:44:23.623 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:45:05.505 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:45:07.610 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:45:07.712 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:45:09.813 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:45:10.042 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:45:12.143 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:45:12.390 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:45:14.483 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:45:14.773 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:45:16.865 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 14:45:17.043 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:26 - initialize Neo4j connection: bolt://localhost:7687
2025-08-24 14:45:19.136 | INFO     | app.lg_agent.kg_sub_graph.kg_neo4j_conn:get_neo4j_graph:36 - LangChain Neo4j graph instance created successfully (APOC working)
2025-08-24 15:12:26.640 | INFO     | app.services.search_service:__init__:17 - Initializing SearchService...
2025-08-24 15:12:30.948 | INFO     | app.services.search_service:generate_stream:99 - Starting search generation for query: 今天的天气怎么样？
2025-08-24 15:12:30.949 | INFO     | app.services.search_service:_call_with_tool:71 - Calling model with query: [{'role': 'system', 'content': "你是一个智能助手，可以通过调用外部的工具获取实时信息。\n你可以使用的工具及使用方法如下：\n\n你现在可用的工具有：\n\nsearch，使用谷歌搜索从互联网获取更多的实时信息，必须解析出来的参数是：query，作用是：通过搜索从互联网获取的信息的问题、内容、关键词等  \n\n当你遇到以下情况时，请调用联网检索工具：\n1. 问题涉及实时数据（如天气、新闻、股票）。\n2. 问题涉及最新事件或动态信息（如'今天发生了什么？'）。\n3. 问题涉及特定领域的外部知识（如法律、医疗、科技）。\n4. 问题中包含'最新'、'当前'、'今天'等时间敏感关键词。\n其他情况下，请直接回答用户的问题。"}, {'role': 'user', 'content': '今天的天气怎么样？'}]
2025-08-24 15:12:30.950 | INFO     | app.services.search_service:_call_with_tool:74 - Messages: [{'role': 'system', 'content': "你是一个智能助手，可以通过调用外部的工具获取实时信息。\n你可以使用的工具及使用方法如下：\n\n你现在可用的工具有：\n\nsearch，使用谷歌搜索从互联网获取更多的实时信息，必须解析出来的参数是：query，作用是：通过搜索从互联网获取的信息的问题、内容、关键词等  \n\n当你遇到以下情况时，请调用联网检索工具：\n1. 问题涉及实时数据（如天气、新闻、股票）。\n2. 问题涉及最新事件或动态信息（如'今天发生了什么？'）。\n3. 问题涉及特定领域的外部知识（如法律、医疗、科技）。\n4. 问题中包含'最新'、'当前'、'今天'等时间敏感关键词。\n其他情况下，请直接回答用户的问题。"}, {'role': 'user', 'content': '今天的天气怎么样？'}]
2025-08-24 15:12:36.269 | INFO     | app.services.search_service:_call_with_tool:83 - Model response: Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='我需要知道您所在的城市或地区才能为您查询今天的天气情况。请告诉我您想了解哪个城市的天气？', refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=None))
2025-08-24 15:12:36.270 | INFO     | app.services.search_service:generate_stream:117 - Tool call response: Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='我需要知道您所在的城市或地区才能为您查询今天的天气情况。请告诉我您想了解哪个城市的天气？', refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=None))
2025-08-24 15:12:36.271 | INFO     | app.services.search_service:generate_stream:189 - Model chose to answer directly, streaming response...
